/**
 * Serviço de notificações para pagamentos e lembretes
 * Integrado com o sistema de notificações real
 */

import { createClient, createAdminClient } from '@/services/supabase/server'
import { NotificationDispatcher, DispatchNotificationData } from '@/services/notifications/channels/notification-dispatcher'

export interface NotificationResult {
  success: boolean
  error?: string
  data?: {
    totalProcessed: number
    remindersSent: number
    overdueNotificationsSent: number
    adminAlertsSent: number
    errors: string[]
  }
}

export interface PaymentReminderResult {
  success: boolean
  error?: string
  data?: {
    totalProcessed: number
    remindersSent: number
    errors: string[]
    reminders: Array<{
      type: 'upcoming' | 'due_today'
      payment_id: string
      student_email: string
      due_date: string
      days_until_due: number
    }>
  }
}

export interface OverdueNotificationResult {
  success: boolean
  error?: string
  data?: {
    totalProcessed: number
    notificationsSent: number
    adminAlertsSent: number
    errors: string[]
    notifications: Array<{
      type: 'student_overdue' | 'admin_alert'
      payment_id: string
      recipient_email: string
      overdue_days: number
      escalation_level: number
    }>
  }
}

export interface ExpenseNotificationResult {
  success: boolean
  error?: string
  data?: {
    totalProcessed: number
    notificationsSent: number
    errors: string[]
    notifications: Array<{
      type: 'upcoming' | 'due_today' | 'overdue'
      expense_id: string
      admin_email: string
      due_date: string
      days_until_due: number
    }>
  }
}



/**
 * Busca o nome do plano do aluno
 */
async function getStudentPlanName(studentId: string): Promise<string> {
  try {
    const supabase = await createAdminClient()

    // Buscar membership ativa/pausada mais recente do aluno
    const { data, error } = await supabase
      .from('memberships')
      .select(`
        id,
        status,
        start_date,
        end_date,
        created_at,
        plans!memberships_plan_id_fkey (
          title,
          plan_type
        )
      `)
      .eq('student_id', studentId)
      .in('status', ['active', 'paused'])
      .order('created_at', { ascending: false })
      .limit(1)
      .single()

    if (error) {
      console.warn(`⚠️ Erro ao buscar plano do aluno ${studentId}:`, error.message)
      return 'Mensalidade'
    }

    if (!data?.plans) {
      console.warn(`⚠️ Nenhum plano encontrado para aluno ${studentId}`)
      return 'Mensalidade'
    }

    const plan = Array.isArray(data.plans) ? data.plans[0] : data.plans
    const planName = plan?.title

    if (!planName) {
      console.warn(`⚠️ Nome do plano não encontrado para aluno ${studentId}`)
      return 'Mensalidade'
    }

    return planName

  } catch (error) {
    console.error(`❌ Erro ao buscar nome do plano para aluno ${studentId}:`, error)
    return 'Mensalidade'
  }
}

/**
 * Busca o nome da academia pelo tenant_id
 */
async function getAcademyName(tenantId: string): Promise<string> {
  try {
    const supabase = await createAdminClient()

    const { data, error } = await supabase
      .from('tenants')
      .select('name')
      .eq('id', tenantId)
      .single()

    if (error) {
      console.warn(`⚠️ Erro ao buscar nome da academia ${tenantId}:`, error.message)
      return 'Academia'
    }

    if (!data?.name) {
      console.warn(`⚠️ Nome da academia não encontrado para tenant ${tenantId}`)
      return 'Academia'
    }

    return data.name

  } catch (error) {
    console.error(`❌ Erro ao buscar nome da academia para tenant ${tenantId}:`, error)
    return 'Academia'
  }
}

/**
 * Processa lembretes de pagamento
 * - 3 dias antes do vencimento
 * - No dia do vencimento
 * Nota: Pagamentos em atraso são tratados pela função processOverdueNotifications
 */
export async function processPaymentReminders(tenantId?: string): Promise<PaymentReminderResult> {
  try {
    const supabase = await createAdminClient()

    console.log(`🔔 Processando lembretes de pagamento para ${tenantId ? `tenant ${tenantId}` : 'todos os tenants'}...`)

    // Buscar pagamentos que precisam de lembrete usando função SQL
    console.log(`🔍 Buscando pagamentos usando função SQL get_payments_for_reminders...`)
    console.log(`   - Tenant: ${tenantId || 'todos'}`)

    const { data: functionResult, error: fetchError } = await supabase
      .rpc('get_payments_for_reminders', {
        p_tenant_id: tenantId || null
      })

    if (fetchError) {
      console.error('❌ Erro ao executar função get_payments_for_reminders:', fetchError)
      return {
        success: false,
        error: `Erro ao buscar pagamentos: ${fetchError.message}`
      }
    }

    if (!functionResult?.success) {
      console.error('❌ Função retornou erro:', functionResult?.error)
      return {
        success: false,
        error: `Erro na função: ${functionResult?.error || 'Erro desconhecido'}`
      }
    }

    console.log(`🔍 Resultado da função SQL:`, JSON.stringify(functionResult, null, 2))

    const paymentsData = functionResult.data
    const paymentsForReminder = paymentsData.all_payments || []

    console.log(`� Função SQL encontrou:`)
    console.log(`   - Data de hoje (timezone SP): ${paymentsData.today}`)
    console.log(`   - Data limite (3 dias): ${paymentsData.three_days_from_now}`)
    console.log(`   - Pagamentos que vencem em 3 dias: ${paymentsData.payments_upcoming?.length || 0}`)
    console.log(`   - Pagamentos que vencem hoje: ${paymentsData.payments_due_today?.length || 0}`)
    console.log(`   - Pagamentos já vencidos: ${paymentsData.payments_overdue?.length || 0}`)
    console.log(`📊 Total combinado: ${paymentsForReminder.length} pagamentos`)
    console.log(`🔍 paymentsForReminder:`, JSON.stringify(paymentsForReminder, null, 2))



    if (paymentsForReminder && paymentsForReminder.length > 0) {
      console.log('📋 Pagamentos encontrados:')
      paymentsForReminder.forEach((payment: any, index: number) => {
        console.log(`   ${index + 1}. ID: ${payment.id}, Due: ${payment.due_date}, Status: ${payment.status}, Type: ${payment.reminder_type}`)
      })
    }

    if (!paymentsForReminder || paymentsForReminder.length === 0) {
      console.log('✅ Nenhum pagamento necessita de lembrete')
      return {
        success: true,
        data: {
          totalProcessed: 0,
          remindersSent: 0,
          errors: [],
          reminders: []
        }
      }
    }

    console.log(`📋 Encontrados ${paymentsForReminder.length} pagamentos para lembrete`)

    const errors: string[] = []
    const reminders: Array<{
      type: 'upcoming' | 'due_today'
      payment_id: string
      student_email: string
      due_date: string
      days_until_due: number
    }> = []

    // Preparar notificações em lote
    interface NotificationWithPaymentData extends DispatchNotificationData {
      paymentData: {
        id: string;
        recipientEmail: string;
        dueDate: string;
        daysUntilDue: number;
        reminderType: 'upcoming' | 'due_today';
      };
    }

    const notificationsToSend: NotificationWithPaymentData[] = []

    // Preparar dados para cada pagamento
    for (const payment of paymentsForReminder) {
      try {
        const student = payment.student

        // Determinar email do destinatário (responsável se menor de idade)
        const recipientEmail = student.is_minor && student.guardian_email
          ? student.guardian_email
          : student.email

        // O tipo de lembrete já vem da função SQL
        const reminderType = payment.reminder_type as 'upcoming' | 'due_today' | 'overdue'
        const daysUntilDue = payment.days_until_due

        // Pular pagamentos em atraso - eles são tratados pela função processOverdueNotifications
        if (reminderType === 'overdue') {
          console.log(`⏭️ Pulando pagamento em atraso ${payment.id} - será tratado pela função de notificações de atraso`)
          continue
        }

        // Buscar tenant_id do usuário na tabela users
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('tenant_id')
          .eq('id', student.user_id)
          .single()

        if (userError || !userData) {
          console.error(`❌ Erro ao buscar tenant_id do usuário ${student.user_id}:`, userError)
          errors.push(`Erro ao buscar dados do usuário ${student.name}: ${userError?.message || 'Usuário não encontrado'}`)
          continue
        }

        const userTenantId = userData.tenant_id

        // Determinar tipo de template baseado no tipo de lembrete
        let templateId: string
        let title: string
        let priority: 'low' | 'medium' | 'high' | 'urgent'

        switch (reminderType) {
          case 'upcoming':
            templateId = 'payment_reminder_upcoming'
            title = 'Lembrete: Pagamento vence em 3 dias'
            priority = 'medium'
            break
          case 'due_today':
            templateId = 'payment_reminder_today'
            title = 'Lembrete: Pagamento vence hoje'
            priority = 'high'
            break
          default:
            console.error(`❌ Tipo de lembrete não suportado: ${reminderType}`)
            continue
        }

        // Buscar nome do plano do aluno
        const planName = await getStudentPlanName(payment.student_id)

        // Buscar nome da academia
        const academyName = await getAcademyName(userTenantId)

        notificationsToSend.push({
          tenantId: userTenantId,
          userId: student.user_id,
          type: 'payment' as const,
          category: 'reminder' as const,
          priority,
          title,
          message: reminderType === 'upcoming'
            ? 'Sua mensalidade vence em 3 dias.'
            : 'Sua mensalidade vence hoje.',
          channels: ['in_app', 'email'] as ('in_app' | 'email')[],
          templateId,
          variables: {
            studentName: student.name,
            amount: parseFloat(payment.amount).toFixed(2),
            dueDate: payment.due_date,
            planName,
            academyName,
            reminderType,
            paymentId: payment.id
          },
          paymentData: {
            id: payment.id,
            recipientEmail,
            dueDate: payment.due_date,
            daysUntilDue,
            reminderType
          }
        })

      } catch (error) {
        const errorMsg = `Erro ao preparar lembrete para pagamento ${payment.id}: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
        console.error('❌', errorMsg)
        errors.push(errorMsg)
      }
    }

    // Enviar notificações em lote
    if (notificationsToSend.length > 0) {
      console.log(`📤 Enviando ${notificationsToSend.length} lembretes em lote...`)

      const dispatcher = new NotificationDispatcher()
      const batchResults = await dispatcher.dispatchBatch(notificationsToSend)

      // Processar resultados do lote
      for (let i = 0; i < batchResults.length; i++) {
        const result = batchResults[i]
        const notificationData = notificationsToSend[i]
        const paymentData = notificationData.paymentData

        if (result.success) {
          console.log(`✅ Lembrete enviado para ${paymentData.recipientEmail} (${paymentData.reminderType})`)

          reminders.push({
            type: paymentData.reminderType,
            payment_id: paymentData.id,
            student_email: paymentData.recipientEmail,
            due_date: paymentData.dueDate,
            days_until_due: paymentData.daysUntilDue
          })
        } else {
          console.error(`❌ Erro ao enviar lembrete para ${paymentData.recipientEmail}:`, result.errors)
          errors.push(`Erro ao enviar lembrete para ${paymentData.recipientEmail}: ${result.errors.join(', ')}`)
        }
      }
    }

    const result = {
      totalProcessed: paymentsForReminder.length,
      remindersSent: reminders.length,
      errors,
      reminders
    }

    console.log(`✅ Processamento de lembretes concluído: ${result.remindersSent}/${result.totalProcessed} lembretes processados`)

    return {
      success: true,
      data: result
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido'
    console.error('💥 Erro crítico ao processar lembretes:', error)
    
    return {
      success: false,
      error: `Erro crítico: ${errorMessage}`
    }
  }
}

/**
 * Processa notificações de pagamentos em atraso
 * - Notifica alunos com escalação (3, 7, 15 dias)
 * - Alerta administradores sobre inadimplência
 */
export async function processOverdueNotifications(tenantId?: string): Promise<OverdueNotificationResult> {
  try {
    const supabase = await createAdminClient()

    console.log(`🚨 Processando notificações de atraso para ${tenantId ? `tenant ${tenantId}` : 'todos os tenants'}...`)

    // Buscar pagamentos em atraso usando função SQL
    console.log(`� Buscando pagamentos em atraso usando função SQL get_overdue_payments_for_notifications...`)
    console.log(`   - Tenant: ${tenantId || 'todos'}`)

    const { data: functionResult, error: fetchError } = await supabase
      .rpc('get_overdue_payments_for_notifications', {
        p_tenant_id: tenantId || null
      })

    if (fetchError) {
      console.error('❌ Erro ao executar função get_overdue_payments_for_notifications:', fetchError)
      return {
        success: false,
        error: `Erro ao buscar pagamentos em atraso: ${fetchError.message}`
      }
    }

    if (!functionResult?.success) {
      console.error('❌ Função retornou erro:', functionResult?.error)
      return {
        success: false,
        error: `Erro na função: ${functionResult?.error || 'Erro desconhecido'}`
      }
    }

    console.log(`🔍 Resultado da função SQL:`, JSON.stringify(functionResult, null, 2))

    const overdueData = functionResult.data
    const overduePayments = overdueData.overdue_payments || []
    const adminUsers = overdueData.admin_users || []
    const escalationPayments = overdueData.escalation_payments || {}

    console.log(`📊 Função SQL encontrou:`)
    console.log(`   - Data de hoje (timezone SP): ${overdueData.today}`)
    console.log(`   - Pagamentos em atraso para notificar: ${overduePayments.length}`)
    console.log(`   - Administradores encontrados: ${adminUsers.length}`)
    console.log(`   - Níveis de escalação: ${Object.keys(escalationPayments).join(', ')}`)

    if (!overduePayments || overduePayments.length === 0) {
      console.log('✅ Nenhum pagamento em atraso encontrado')
      return {
        success: true,
        data: {
          totalProcessed: 0,
          notificationsSent: 0,
          adminAlertsSent: 0,
          errors: [],
          notifications: []
        }
      }
    }

    console.log(`📋 Encontrados ${overduePayments.length} pagamentos em atraso para notificar`)

    const errors: string[] = []
    const notifications: Array<{
      type: 'student_overdue' | 'admin_alert'
      payment_id: string
      recipient_email: string
      overdue_days: number
      escalation_level: number
    }> = []

    // Preparar notificações de atraso em lote
    interface OverdueNotificationWithPaymentData extends DispatchNotificationData {
      paymentData: {
        id: string;
        recipientEmail: string;
        overdueDays: number;
        escalationLevel: number;
      };
    }

    const overdueNotificationsToSend: OverdueNotificationWithPaymentData[] = []

    // Preparar dados para cada pagamento em atraso
    for (const payment of overduePayments) {
      try {
        const student = payment.student
        const overdueDays = payment.overdue_days
        const escalationLevel = payment.escalation_level

        // Determinar email do destinatário
        const recipientEmail = student.is_minor && student.guardian_email
          ? student.guardian_email
          : student.email

        // A função SQL já filtra apenas pagamentos que devem ser notificados
        if (payment.should_notify && escalationLevel > 0) {
          // Determinar template e prioridade baseado no nível de escalação
          let templateId: string
          let title: string
          let priority: 'low' | 'medium' | 'high' | 'urgent'

          switch (escalationLevel) {
            case 1:
              templateId = 'payment_overdue_level_1'
              title = 'Pagamento em atraso - 3 dias'
              priority = 'high'
              break
            case 2:
              templateId = 'payment_overdue_level_2'
              title = 'Pagamento em atraso - 7 dias'
              priority = 'urgent'
              break
            case 3:
              templateId = 'payment_overdue_level_3'
              title = 'Pagamento em atraso - 15 dias'
              priority = 'urgent'
              break
            default:
              templateId = 'payment_overdue_general'
              title = 'Pagamento em atraso'
              priority = 'urgent'
          }

          // Buscar nome do plano do aluno
          const planName = await getStudentPlanName(payment.student_id)

          // Buscar nome da academia
          const academyName = await getAcademyName(payment.tenant_id)

          overdueNotificationsToSend.push({
            tenantId: payment.tenant_id,
            userId: student.user_id,
            type: 'payment',
            category: 'alert',
            priority,
            title,
            message: `Seu pagamento está em atraso há ${overdueDays} dias.`,
            channels: ['in_app', 'email'],
            templateId,
            variables: {
              studentName: student.name,
              amount: parseFloat(payment.amount).toFixed(2),
              dueDate: payment.due_date,
              overdueDays,
              escalationLevel,
              planName,
              academyName,
              paymentId: payment.id
            },
            paymentData: {
              id: payment.id,
              recipientEmail,
              overdueDays,
              escalationLevel
            }
          })
        }

      } catch (error) {
        const errorMsg = `Erro ao preparar notificação para pagamento ${payment.id}: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
        console.error('❌', errorMsg)
        errors.push(errorMsg)
      }
    }

    // Enviar notificações de atraso em lote
    if (overdueNotificationsToSend.length > 0) {
      console.log(`📤 Enviando ${overdueNotificationsToSend.length} notificações de atraso em lote...`)

      const dispatcher = new NotificationDispatcher()
      const overdueBatchResults = await dispatcher.dispatchBatch(overdueNotificationsToSend)

      // Processar resultados do lote
      for (let i = 0; i < overdueBatchResults.length; i++) {
        const result = overdueBatchResults[i]
        const notificationData = overdueNotificationsToSend[i]
        const paymentData = notificationData.paymentData

        if (result.success) {
          console.log(`✅ Notificação de atraso nível ${paymentData.escalationLevel} enviada para ${paymentData.recipientEmail}`)

          notifications.push({
            type: 'student_overdue',
            payment_id: paymentData.id,
            recipient_email: paymentData.recipientEmail,
            overdue_days: paymentData.overdueDays,
            escalation_level: paymentData.escalationLevel
          })
        } else {
          console.error(`❌ Erro ao enviar notificação de atraso para ${paymentData.recipientEmail}:`, result.errors)
          errors.push(`Erro ao enviar notificação de atraso para ${paymentData.recipientEmail}: ${result.errors.join(', ')}`)
        }
      }
    }

    // Processar alertas para administradores nas escalações
    let adminAlertsSent = 0
    if (adminUsers && adminUsers.length > 0 && overduePayments.length > 0) {
      // Agrupar pagamentos por nível de escalação para alertas admin
      const escalationGroups: Record<number, { count: number; totalAtRisk: number; payments: any[] }> = {}

      for (const payment of overduePayments) {
        const escalationLevel = payment.escalation_level
        if (escalationLevel > 0) {
          if (!escalationGroups[escalationLevel]) {
            escalationGroups[escalationLevel] = { count: 0, totalAtRisk: 0, payments: [] }
          }
          escalationGroups[escalationLevel].count++
          escalationGroups[escalationLevel].totalAtRisk += parseFloat(payment.amount)
          escalationGroups[escalationLevel].payments.push(payment)
        }
      }

      // Buscar nome da academia
      const academyName = await getAcademyName(tenantId || overduePayments[0]?.tenant_id)

      // Preparar alertas administrativos em lote
      interface AdminAlertWithData extends DispatchNotificationData {
        adminData: {
          email: string;
          escalationLevel: number;
        };
      }

      const adminAlertsToSend: AdminAlertWithData[] = []

      // Preparar alertas para cada nível de escalação
      for (const [level, groupData] of Object.entries(escalationGroups)) {
        const escalationLevel = parseInt(level)
        const { count: overdueCount, totalAtRisk } = groupData

        let title: string
        let templateId: string

        switch (escalationLevel) {
          case 1:
            title = 'Alerta: Pagamentos em atraso - 3 dias'
            templateId = 'admin_overdue_alert_level_1'
            break
          case 2:
            title = 'Alerta: Pagamentos em atraso - 7 dias'
            templateId = 'admin_overdue_alert_level_2'
            break
          case 3:
            title = 'Alerta: Pagamentos em atraso crítico - 15 dias'
            templateId = 'admin_overdue_alert_level_3'
            break
          default:
            title = 'Alerta: Pagamentos em atraso'
            templateId = 'admin_overdue_alert'
        }

        for (const admin of adminUsers) {
          adminAlertsToSend.push({
            tenantId: tenantId || overduePayments[0]?.tenant_id,
            userId: admin.id,
            type: 'system',
            category: 'alert',
            priority: 'urgent',
            title,
            message: `${overdueCount} pagamentos com atraso de ${escalationLevel === 1 ? '3' : escalationLevel === 2 ? '7' : '15'}+ dias. Total em risco: R$ ${totalAtRisk.toFixed(2)}`,
            channels: ['in_app', 'email'],
            templateId,
            variables: {
              adminName: `${admin.first_name} ${admin.last_name}`,
              overdueCount,
              totalAtRisk: totalAtRisk.toFixed(2),
              escalationLevel,
              academyName,
              alertType: 'overdue_payments',
              alertTitle: title,
              alertMessage: `${overdueCount} pagamentos com atraso de ${escalationLevel === 1 ? '3' : escalationLevel === 2 ? '7' : '15'}+ dias. Total em risco: R$ ${totalAtRisk.toFixed(2)}`,
              priority: 'urgent',
              currency: 'BRL'
            },
            adminData: {
              email: admin.email,
              escalationLevel
            }
          })
        }
      }

      // Enviar alertas administrativos em lote
      if (adminAlertsToSend.length > 0) {
        console.log(`📤 Enviando ${adminAlertsToSend.length} alertas administrativos em lote...`)

        const dispatcher = new NotificationDispatcher()
        const adminBatchResults = await dispatcher.dispatchBatch(adminAlertsToSend)

        // Processar resultados do lote
        for (let i = 0; i < adminBatchResults.length; i++) {
          const result = adminBatchResults[i]
          const alertData = adminAlertsToSend[i]
          const adminData = alertData.adminData

          if (result.success) {
            console.log(`✅ Alerta administrativo nível ${adminData.escalationLevel} enviado para ${adminData.email}`)

            notifications.push({
              type: 'admin_alert',
              payment_id: 'multiple',
              recipient_email: adminData.email,
              overdue_days: adminData.escalationLevel === 1 ? 3 : adminData.escalationLevel === 2 ? 7 : 15,
              escalation_level: adminData.escalationLevel
            })

            adminAlertsSent++
          } else {
            console.error(`❌ Erro ao enviar alerta para admin ${adminData.email}:`, result.errors)
            errors.push(`Erro ao enviar alerta para admin ${adminData.email}: ${result.errors.join(', ')}`)
          }
        }
      }
    }

    const result = {
      totalProcessed: overduePayments.length,
      notificationsSent: notifications.filter(n => n.type === 'student_overdue').length,
      adminAlertsSent,
      errors,
      notifications
    }

    console.log(`✅ Processamento de notificações concluído:`)
    console.log(`  - Total processados: ${result.totalProcessed}`)
    console.log(`  - Notificações enviadas: ${result.notificationsSent}`)
    console.log(`  - Alertas admin enviados: ${result.adminAlertsSent}`)

    return {
      success: true,
      data: result
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido'
    console.error('💥 Erro crítico ao processar notificações de atraso:', error)
    
    return {
      success: false,
      error: `Erro crítico: ${errorMessage}`
    }
  }
}

/**
 * Processa notificações de despesas da academia
 * - Notifica administradores sobre despesas que vencem em 3 dias, hoje ou já venceram
 */
export async function processExpenseNotifications(tenantId?: string): Promise<ExpenseNotificationResult> {
  try {
    const supabase = await createAdminClient()

    console.log(`💼 Processando notificações de despesas para ${tenantId ? `tenant ${tenantId}` : 'todos os tenants'}...`)

    // Usar data e hora de Brasília (UTC-3)
    const now = new Date()
    const brasiliaOffset = -3 * 60 // UTC-3 em minutos
    const brasiliaTime = new Date(now.getTime() + (brasiliaOffset * 60 * 1000))

    const today = brasiliaTime.toISOString().split('T')[0]
    const threeDaysFromNow = new Date(brasiliaTime)
    threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3)
    const threeDaysFromNowStr = threeDaysFromNow.toISOString().split('T')[0]

    console.log(`📅 Datas de referência:`)
    console.log(`   - Hoje (Brasília): ${today}`)
    console.log(`   - Em 3 dias: ${threeDaysFromNowStr}`)

    // Buscar despesas que precisam de notificação
    let query = supabase
      .from('expenses')
      .select(`
        id,
        tenant_id,
        supplier_name,
        amount,
        currency,
        description,
        due_date,
        status,
        expense_categories (
          id,
          name,
          color
        ),
        tenants!expenses_tenant_id_fkey (
          name,
          owner_id
        )
      `)
      .in('status', ['pending', 'overdue'])
      .or(`due_date.eq.${threeDaysFromNowStr},due_date.eq.${today},due_date.lt.${today}`)

    if (tenantId) {
      query = query.eq('tenant_id', tenantId)
    }

    const { data: expenses, error: expensesError } = await query

    if (expensesError) {
      console.error('❌ Erro ao buscar despesas:', expensesError)
      return {
        success: false,
        error: `Erro ao buscar despesas: ${expensesError.message}`
      }
    }

    if (!expenses || expenses.length === 0) {
      console.log('✅ Nenhuma despesa necessita de notificação')
      return {
        success: true,
        data: {
          totalProcessed: 0,
          notificationsSent: 0,
          errors: [],
          notifications: []
        }
      }
    }

    console.log(`📋 Encontradas ${expenses.length} despesas para notificar`)

    const errors: string[] = []
    const notifications: Array<{
      type: 'upcoming' | 'due_today' | 'overdue'
      expense_id: string
      admin_email: string
      due_date: string
      days_until_due: number
    }> = []

    // Agrupar despesas por tenant para otimizar
    const expensesByTenant = expenses.reduce((acc, expense) => {
      if (!acc[expense.tenant_id]) {
        acc[expense.tenant_id] = []
      }
      acc[expense.tenant_id].push(expense)
      return acc
    }, {} as Record<string, typeof expenses>)

    // Preparar notificações em lote
    interface ExpenseNotificationWithData extends DispatchNotificationData {
      expenseData: {
        id: string;
        adminEmail: string;
        dueDate: string;
        daysUntilDue: number;
        notificationType: 'upcoming' | 'due_today' | 'overdue';
      };
    }

    const notificationsToSend: ExpenseNotificationWithData[] = []

    // Processar cada tenant
    for (const [currentTenantId, tenantExpenses] of Object.entries(expensesByTenant)) {
      try {
        // Buscar administradores do tenant
        const { data: adminUsers, error: adminError } = await supabase
          .from('users')
          .select('id, email, first_name, last_name')
          .eq('tenant_id', currentTenantId)
          .eq('role', 'admin')
          .is('deleted_at', null)

        if (adminError) {
          console.error(`❌ Erro ao buscar administradores do tenant ${currentTenantId}:`, adminError)
          errors.push(`Erro ao buscar administradores do tenant ${currentTenantId}: ${adminError.message}`)
          continue
        }

        if (!adminUsers || adminUsers.length === 0) {
          console.warn(`⚠️ Nenhum administrador encontrado para tenant ${currentTenantId}`)
          errors.push(`Nenhum administrador encontrado para tenant ${currentTenantId}`)
          continue
        }

        console.log(`👥 Encontrados ${adminUsers.length} administradores para tenant ${currentTenantId}`)

        // Buscar nome da academia
        const academyName = await getAcademyName(currentTenantId)

        // Processar cada despesa do tenant
        for (const expense of tenantExpenses) {
          try {
            // Determinar tipo de notificação e dias até vencimento
            let notificationType: 'upcoming' | 'due_today' | 'overdue'
            let daysUntilDue: number

            if (expense.due_date === threeDaysFromNowStr) {
              notificationType = 'upcoming'
              daysUntilDue = 3
            } else if (expense.due_date === today) {
              notificationType = 'due_today'
              daysUntilDue = 0
            } else {
              notificationType = 'overdue'
              const dueDate = new Date(expense.due_date)
              const todayDate = new Date(today)
              daysUntilDue = Math.floor((todayDate.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24))
            }

            // Determinar template e prioridade
            let templateId: string
            let title: string
            let priority: 'low' | 'medium' | 'high' | 'urgent'

            switch (notificationType) {
              case 'upcoming':
                templateId = 'expense_due'
                title = 'Lembrete: Despesa vence em 3 dias'
                priority = 'medium'
                break
              case 'due_today':
                templateId = 'expense_due'
                title = 'Lembrete: Despesa vence hoje'
                priority = 'high'
                break
              case 'overdue':
                templateId = 'expense_due'
                title = 'Alerta: Despesa em atraso'
                priority = 'urgent'
                break
            }

            // Preparar dados para o template
            // expense_categories pode vir como array ou objeto dependendo da query
            const categoryData = Array.isArray(expense.expense_categories)
              ? expense.expense_categories[0]
              : expense.expense_categories;
            const category = categoryData?.name || 'Outros'

            // Criar notificação para cada administrador
            for (const admin of adminUsers) {
              notificationsToSend.push({
                tenantId: currentTenantId,
                userId: admin.id,
                type: 'expense_due',
                category: notificationType === 'overdue' ? 'alert' : 'reminder',
                priority,
                title,
                message: notificationType === 'upcoming'
                  ? 'Uma despesa da academia vence em 3 dias.'
                  : notificationType === 'due_today'
                  ? 'Uma despesa da academia vence hoje.'
                  : `Uma despesa da academia está em atraso há ${Math.abs(daysUntilDue)} dias.`,
                channels: ['in_app', 'email'],
                templateId,
                variables: {
                  academyName,
                  ownerName: `${admin.first_name} ${admin.last_name}`,
                  expenseDescription: expense.description || 'Despesa',
                  amount: parseFloat(expense.amount),
                  dueDate: expense.due_date,
                  category,
                  supplier: expense.supplier_name,
                  currency: expense.currency || 'BRL',
                  // Propriedades específicas para o template expense-due
                  isOverdue: notificationType === 'overdue',
                  daysOverdue: notificationType === 'overdue' ? Math.abs(daysUntilDue) : 0,
                  dashboardUrl: `${process.env.NEXT_PUBLIC_BASE_DOMAIN}/financeiro/dashboard`
                },
                expenseData: {
                  id: expense.id,
                  adminEmail: admin.email,
                  dueDate: expense.due_date,
                  daysUntilDue,
                  notificationType
                }
              })
            }

          } catch (error) {
            const errorMsg = `Erro ao preparar notificação para despesa ${expense.id}: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
            console.error('❌', errorMsg)
            errors.push(errorMsg)
          }
        }

      } catch (error) {
        const errorMsg = `Erro ao processar tenant ${currentTenantId}: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
        console.error('❌', errorMsg)
        errors.push(errorMsg)
      }
    }

    // Enviar notificações em lote
    if (notificationsToSend.length > 0) {
      console.log(`📤 Enviando ${notificationsToSend.length} notificações de despesas em lote...`)

      const dispatcher = new NotificationDispatcher()
      const batchResults = await dispatcher.dispatchBatch(notificationsToSend)

      // Processar resultados do lote
      for (let i = 0; i < batchResults.length; i++) {
        const result = batchResults[i]
        const notificationData = notificationsToSend[i]
        const expenseData = notificationData.expenseData

        if (result.success) {
          console.log(`✅ Notificação de despesa (${expenseData.notificationType}) enviada para ${expenseData.adminEmail}`)

          notifications.push({
            type: expenseData.notificationType,
            expense_id: expenseData.id,
            admin_email: expenseData.adminEmail,
            due_date: expenseData.dueDate,
            days_until_due: expenseData.daysUntilDue
          })
        } else {
          console.error(`❌ Erro ao enviar notificação para ${expenseData.adminEmail}:`, result.errors)
          errors.push(`Erro ao enviar notificação para ${expenseData.adminEmail}: ${result.errors.join(', ')}`)
        }
      }
    }

    const result = {
      totalProcessed: expenses.length,
      notificationsSent: notifications.length,
      errors,
      notifications
    }

    console.log(`✅ Processamento de notificações de despesas concluído: ${result.notificationsSent} notificações enviadas para ${result.totalProcessed} despesas`)

    return {
      success: true,
      data: result
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido'
    console.error('💥 Erro crítico ao processar notificações de despesas:', error)

    return {
      success: false,
      error: `Erro crítico: ${errorMessage}`
    }
  }
}

/**
 * Processa todas as notificações (lembretes + atrasos)
 */
export async function processAllNotifications(tenantId?: string): Promise<NotificationResult> {
  try {
    console.log(`🚀 Iniciando processamento completo de notificações...`)

    const [remindersResult, overdueResult] = await Promise.all([
      processPaymentReminders(tenantId),
      processOverdueNotifications(tenantId)
    ])

    const errors: string[] = []

    if (!remindersResult.success) {
      errors.push(`Lembretes: ${remindersResult.error}`)
    }

    if (!overdueResult.success) {
      errors.push(`Notificações de atraso: ${overdueResult.error}`)
    }

    // Combinar erros dos processamentos individuais
    if (remindersResult.data?.errors) {
      errors.push(...remindersResult.data.errors)
    }

    if (overdueResult.data?.errors) {
      errors.push(...overdueResult.data.errors)
    }

    const totalProcessed = (remindersResult.data?.totalProcessed || 0) + (overdueResult.data?.totalProcessed || 0)
    const remindersSent = remindersResult.data?.remindersSent || 0
    const overdueNotificationsSent = overdueResult.data?.notificationsSent || 0
    const adminAlertsSent = overdueResult.data?.adminAlertsSent || 0

    console.log(`✅ Processamento completo de notificações finalizado:`)
    console.log(`  - Total processados: ${totalProcessed}`)
    console.log(`  - Lembretes enviados: ${remindersSent}`)
    console.log(`  - Notificações de atraso enviadas: ${overdueNotificationsSent}`)
    console.log(`  - Alertas admin enviados: ${adminAlertsSent}`)
    console.log(`  - Erros: ${errors.length}`)

    return {
      success: remindersResult.success && overdueResult.success,
      data: {
        totalProcessed,
        remindersSent,
        overdueNotificationsSent,
        adminAlertsSent,
        errors
      }
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido'
    console.error('💥 Erro crítico no processamento completo de notificações:', error)

    return {
      success: false,
      error: `Erro crítico: ${errorMessage}`
    }
  }
}
