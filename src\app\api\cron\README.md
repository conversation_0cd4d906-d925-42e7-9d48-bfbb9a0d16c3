# Cron Jobs - Sistema de Pagamentos e Notificações

Este diretório contém os endpoints de cron jobs para automatizar o processamento de pagamentos, despesas e notificações do sistema.

## Endpoints Disponíveis

### 1. Process Overdue Payments
**Endpoint:** `/api/cron/process-overdue-payments`  
**Execução:** Diário às 06:00  
**Função:** Processar pagamentos em atraso

#### Funcionalidades:
- Identifica pagamentos com status `pending` e `due_date` anterior à data atual
- Atualiza status para `overdue`
- Define `overdue_date` com a data atual
- Aplica multas/juros se configurado (futuro)

#### Exemplo de uso:
```bash
curl -X POST https://seu-dominio.com/api/cron/process-overdue-payments \
  -H "Authorization: Bearer YOUR_CRON_SECRET_KEY"
```

### 2. Process Overdue Expenses
**Endpoint:** `/api/cron/process-overdue-expenses`  
**Execução:** <PERSON><PERSON><PERSON> às 06:00  
**Função:** Processar despesas em atraso

#### Funcionalidades:
- Identifica despesas com status `pending` e `due_date` anterior à data atual
- Atualiza status para `overdue`

#### Exemplo de uso:
```bash
curl -X POST https://seu-dominio.com/api/cron/process-overdue-expenses \
  -H "Authorization: Bearer YOUR_CRON_SECRET_KEY"
```

### 3. Payment Reminders
**Endpoint:** `/api/cron/payment-reminders`  
**Execução:** Diário às 08:00  
**Função:** Enviar lembretes de pagamento

#### Funcionalidades:
- Envia lembretes 3 dias antes do vencimento (configurável no futuro)
- Envia lembretes no dia do vencimento
- Notifica pagamentos em atraso
- Considera responsáveis para menores de idade

#### Exemplo de uso:
```bash
curl -X POST https://seu-dominio.com/api/cron/payment-reminders \
  -H "Authorization: Bearer YOUR_CRON_SECRET_KEY"
```

### 4. Overdue Notifications
**Endpoint:** `/api/cron/overdue-notifications`  
**Execução:** Diário às 10:00  
**Função:** Notificações de atraso com escalação

#### Funcionalidades:
- Notifica alunos com pagamentos atrasados
- Sistema de escalação por dias de atraso:
  - **Nível 1:** 3-6 dias de atraso
  - **Nível 2:** 7-14 dias de atraso  
  - **Nível 3:** 15+ dias de atraso
- Alerta administradores sobre inadimplência crítica (15+ dias)

#### Exemplo de uso:
```bash
curl -X POST https://seu-dominio.com/api/cron/overdue-notifications \
  -H "Authorization: Bearer YOUR_CRON_SECRET_KEY"
```

### 5. Expense Notifications
**Endpoint:** `/api/cron/expense-notifications`
**Execução:** Diário às 08:30
**Função:** Enviar notificações de despesas da academia

#### Funcionalidades:
- Notifica administradores sobre despesas que vencem em 3 dias
- Notifica administradores sobre despesas que vencem hoje
- Notifica administradores sobre despesas em atraso
- Utiliza template de email específico para despesas
- Sistema de priorização baseado no status da despesa

#### Exemplo de uso:
```bash
curl -X POST https://seu-dominio.com/api/cron/expense-notifications \
  -H "Authorization: Bearer YOUR_CRON_SECRET_KEY"
```

### 6. Process Recurring Payments
**Endpoint:** `/api/cron/process-recurring-payments`
**Execução:** Diário às 00:30
**Função:** Processar pagamentos recorrentes pendentes

#### Funcionalidades:
- Processa pagamentos que deveriam ter sido criados mas não foram devido a pagamentos antecipados
- Cria pagamentos para ciclos que já venceram
- Corrige inconsistências no sistema de pagamentos recorrentes
- Sistema de retry automático para maior robustez
- Monitora e registra estatísticas de processamento

#### Exemplo de uso:
```bash
curl -X POST https://seu-dominio.com/api/cron/process-recurring-payments \
  -H "Authorization: Bearer YOUR_CRON_SECRET_KEY"
```

## Configuração

### Variáveis de Ambiente
Adicione ao seu arquivo `.env`:

```env
CRON_SECRET_KEY=sua_chave_secreta_super_segura_aqui
```

### Configuração do Cron (Vercel)
No arquivo `vercel.json`, adicione:

```json
{
  "crons": [
    {
      "path": "/api/cron/process-recurring-payments",
      "schedule": "30 0 * * *"
    },
    {
      "path": "/api/cron/process-overdue-payments",
      "schedule": "0 6 * * *"
    },
    {
      "path": "/api/cron/process-overdue-expenses",
      "schedule": "0 6 * * *"
    },
    {
      "path": "/api/cron/payment-reminders",
      "schedule": "0 8 * * *"
    },
    {
      "path": "/api/cron/expense-notifications",
      "schedule": "30 8 * * *"
    },
    {
      "path": "/api/cron/overdue-notifications",
      "schedule": "0 10 * * *"
    }
  ]
}
```

### Configuração Manual (Outros Provedores)
Para outros provedores de hosting, configure os cron jobs para fazer requisições HTTP:

```bash
# Processar pagamentos em atraso - 06:00 diário
0 6 * * * curl -X POST https://seu-dominio.com/api/cron/process-overdue-payments -H "Authorization: Bearer YOUR_CRON_SECRET_KEY"

# Processar despesas em atraso - 06:00 diário  
0 6 * * * curl -X POST https://seu-dominio.com/api/cron/process-overdue-expenses -H "Authorization: Bearer YOUR_CRON_SECRET_KEY"

# Lembretes de pagamento - 08:00 diário
0 8 * * * curl -X POST https://seu-dominio.com/api/cron/payment-reminders -H "Authorization: Bearer YOUR_CRON_SECRET_KEY"

# Notificações de despesas - 08:30 diário
30 8 * * * curl -X POST https://seu-dominio.com/api/cron/expense-notifications -H "Authorization: Bearer YOUR_CRON_SECRET_KEY"

# Notificações de atraso - 10:00 diário
0 10 * * * curl -X POST https://seu-dominio.com/api/cron/overdue-notifications -H "Authorization: Bearer YOUR_CRON_SECRET_KEY"
```

## Monitoramento

### Verificar Status
Todos os endpoints suportam requisições GET para verificar status:

```bash
curl -X GET https://seu-dominio.com/api/cron/process-overdue-payments \
  -H "Authorization: Bearer YOUR_CRON_SECRET_KEY"
```

### Resposta de Status
```json
{
  "status": "healthy",
  "isRunning": false,
  "runningBy": null,
  "lockExpiresAt": null,
  "executionCount": 42,
  "paymentsNeedingProcessing": {
    "count": 5,
    "totalAmount": 750.00
  },
  "timestamp": "2025-07-25T10:00:00.000Z"
}
```

## Sistema de Lock

Todos os cron jobs implementam um sistema de lock para evitar execuções simultâneas:

- **Timeout:** 5 minutos por execução
- **Lock automático:** Previne sobreposição de execuções
- **Identificação única:** Cada execução tem um ID único para rastreamento

## Logs

Os cron jobs geram logs detalhados:

```
🔄 [abc123] Iniciando cron job de processamento de pagamentos em atraso...
🚀 [abc123] Execução #42 autorizada
📋 Encontrados 5 pagamentos em atraso
✅ Pagamento 4cd7585f-e66f-422a-90b6-1f812a8622ea marcado como em atraso (5 dias)
✅ [abc123] Cron job concluído em 1250ms:
  - Total processados: 5
  - Atualizados: 5
  - Erros: 0
```

## Integração com Sistema de Notificações

**Status:** Preparado para integração futura

Os serviços de notificação estão estruturados para integração com:
- Sistema de email (SendGrid, AWS SES, etc.)
- SMS (Twilio, AWS SNS, etc.)
- Push notifications
- WhatsApp Business API

Atualmente, as notificações são simuladas nos logs para desenvolvimento e teste.

## Segurança

- **Autenticação:** Chave secreta obrigatória
- **Rate limiting:** Sistema de lock previne spam
- **Logs seguros:** Não expõem dados sensíveis
- **Validação:** Entrada validada em todas as requisições

## Troubleshooting

### Erro 401 - Não autorizado
- Verifique se `CRON_SECRET_KEY` está configurada
- Confirme se o header `Authorization: Bearer` está correto

### Erro 409 - Lock não adquirido
- Outra execução está em andamento
- Aguarde o timeout (5 minutos) ou verifique logs

### Erro 500 - Erro interno
- Verifique logs do servidor
- Confirme conectividade com banco de dados
- Valide permissões de acesso às tabelas

## Desenvolvimento

Para testar localmente:

```bash
# Instalar dependências
npm install

# Configurar variáveis de ambiente
cp .env.example .env.local

# Executar em modo desenvolvimento
npm run dev

# Testar endpoint
curl -X POST http://localhost:3000/api/cron/process-overdue-payments \
  -H "Authorization: Bearer sua_chave_local"
```
