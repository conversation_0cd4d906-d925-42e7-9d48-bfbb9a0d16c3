export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  public: {
    Tables: {
      ai_chat_usage: {
        Row: {
          created_at: string | null
          id: string
          message_count: number
          tenant_id: string
          updated_at: string | null
          usage_date: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          message_count?: number
          tenant_id: string
          updated_at?: string | null
          usage_date?: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          message_count?: number
          tenant_id?: string
          updated_at?: string | null
          usage_date?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "ai_chat_usage_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      attendance: {
        Row: {
          checked_in_at: string
          checked_in_by: string
          class_id: string
          created_at: string
          id: string
          notes: string | null
          student_id: string
          tenant_id: string
        }
        Insert: {
          checked_in_at: string
          checked_in_by: string
          class_id: string
          created_at?: string
          id?: string
          notes?: string | null
          student_id: string
          tenant_id: string
        }
        Update: {
          checked_in_at?: string
          checked_in_by?: string
          class_id?: string
          created_at?: string
          id?: string
          notes?: string | null
          student_id?: string
          tenant_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "attendance_checked_in_by_fkey"
            columns: ["checked_in_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_class_id_fkey"
            columns: ["class_id"]
            isOneToOne: false
            referencedRelation: "classes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_class_id_fkey"
            columns: ["class_id"]
            isOneToOne: false
            referencedRelation: "student_class_eligibility"
            referencedColumns: ["class_id"]
          },
          {
            foreignKeyName: "attendance_student_id_fkey"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "student_current_belt_details"
            referencedColumns: ["student_id"]
          },
          {
            foreignKeyName: "attendance_student_id_fkey"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "students"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      belt_levels: {
        Row: {
          belt_color: string
          center_line_color: string | null
          created_at: string
          degree: number
          id: string
          label: string
          modality_id: string
          show_center_line: boolean | null
          sort_order: number
          stripe_color: string | null
          tenant_id: string | null
          updated_at: string | null
        }
        Insert: {
          belt_color: string
          center_line_color?: string | null
          created_at?: string
          degree?: number
          id?: string
          label: string
          modality_id: string
          show_center_line?: boolean | null
          sort_order: number
          stripe_color?: string | null
          tenant_id?: string | null
          updated_at?: string | null
        }
        Update: {
          belt_color?: string
          center_line_color?: string | null
          created_at?: string
          degree?: number
          id?: string
          label?: string
          modality_id?: string
          show_center_line?: boolean | null
          sort_order?: number
          stripe_color?: string | null
          tenant_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "belt_levels_modality_id_fkey"
            columns: ["modality_id"]
            isOneToOne: false
            referencedRelation: "modalities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "belt_levels_tenant_fk"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      branches: {
        Row: {
          address: string | null
          city: string | null
          complement: string | null
          created_at: string
          deleted_at: string | null
          email: string | null
          id: string
          is_main: boolean | null
          name: string
          neighborhood: string | null
          phone: string | null
          postal_code: string | null
          state: string | null
          street: string | null
          street_number: string | null
          tenant_id: string
          updated_at: string | null
        }
        Insert: {
          address?: string | null
          city?: string | null
          complement?: string | null
          created_at?: string
          deleted_at?: string | null
          email?: string | null
          id?: string
          is_main?: boolean | null
          name: string
          neighborhood?: string | null
          phone?: string | null
          postal_code?: string | null
          state?: string | null
          street?: string | null
          street_number?: string | null
          tenant_id: string
          updated_at?: string | null
        }
        Update: {
          address?: string | null
          city?: string | null
          complement?: string | null
          created_at?: string
          deleted_at?: string | null
          email?: string | null
          id?: string
          is_main?: boolean | null
          name?: string
          neighborhood?: string | null
          phone?: string | null
          postal_code?: string | null
          state?: string | null
          street?: string | null
          street_number?: string | null
          tenant_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "branches_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      class_group_enrollments: {
        Row: {
          class_group_id: string
          created_at: string
          effective_from_date: string | null
          enrollment_date: string
          id: string
          metadata: Json | null
          notes: string | null
          status: string
          student_id: string
          tenant_id: string
          updated_at: string
        }
        Insert: {
          class_group_id: string
          created_at?: string
          effective_from_date?: string | null
          enrollment_date?: string
          id?: string
          metadata?: Json | null
          notes?: string | null
          status?: string
          student_id: string
          tenant_id: string
          updated_at?: string
        }
        Update: {
          class_group_id?: string
          created_at?: string
          effective_from_date?: string | null
          enrollment_date?: string
          id?: string
          metadata?: Json | null
          notes?: string | null
          status?: string
          student_id?: string
          tenant_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "class_group_enrollments_class_group_id_fkey"
            columns: ["class_group_id"]
            isOneToOne: false
            referencedRelation: "class_groups"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "class_group_enrollments_student_id_fkey"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "student_current_belt_details"
            referencedColumns: ["student_id"]
          },
          {
            foreignKeyName: "class_group_enrollments_student_id_fkey"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "students"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "class_group_enrollments_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      class_groups: {
        Row: {
          allow_waitlist: boolean
          branch_id: string
          category: string | null
          created_at: string | null
          deleted_at: string | null
          description: string | null
          end_date: string | null
          id: string
          instructor_id: string
          is_active: boolean | null
          max_age: number | null
          max_belt_level: string | null
          max_capacity: number | null
          metadata: Json | null
          min_age: number | null
          min_belt_level: string | null
          name: string
          recurrence_pattern: Json | null
          start_date: string | null
          tenant_id: string
          updated_at: string | null
        }
        Insert: {
          allow_waitlist?: boolean
          branch_id: string
          category?: string | null
          created_at?: string | null
          deleted_at?: string | null
          description?: string | null
          end_date?: string | null
          id?: string
          instructor_id: string
          is_active?: boolean | null
          max_age?: number | null
          max_belt_level?: string | null
          max_capacity?: number | null
          metadata?: Json | null
          min_age?: number | null
          min_belt_level?: string | null
          name: string
          recurrence_pattern?: Json | null
          start_date?: string | null
          tenant_id: string
          updated_at?: string | null
        }
        Update: {
          allow_waitlist?: boolean
          branch_id?: string
          category?: string | null
          created_at?: string | null
          deleted_at?: string | null
          description?: string | null
          end_date?: string | null
          id?: string
          instructor_id?: string
          is_active?: boolean | null
          max_age?: number | null
          max_belt_level?: string | null
          max_capacity?: number | null
          metadata?: Json | null
          min_age?: number | null
          min_belt_level?: string | null
          name?: string
          recurrence_pattern?: Json | null
          start_date?: string | null
          tenant_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "class_groups_branch_id_fkey"
            columns: ["branch_id"]
            isOneToOne: false
            referencedRelation: "branches"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "class_groups_instructor_id_fkey"
            columns: ["instructor_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "class_groups_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      class_qr_codes: {
        Row: {
          class_id: string
          created_at: string | null
          created_by: string
          expires_at: string
          id: string
          is_active: boolean | null
          qr_code: string
          raw_qr_data: Json
          tenant_id: string
          updated_at: string | null
        }
        Insert: {
          class_id: string
          created_at?: string | null
          created_by: string
          expires_at: string
          id?: string
          is_active?: boolean | null
          qr_code: string
          raw_qr_data: Json
          tenant_id: string
          updated_at?: string | null
        }
        Update: {
          class_id?: string
          created_at?: string | null
          created_by?: string
          expires_at?: string
          id?: string
          is_active?: boolean | null
          qr_code?: string
          raw_qr_data?: Json
          tenant_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "class_qr_codes_class_id_fkey"
            columns: ["class_id"]
            isOneToOne: false
            referencedRelation: "classes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "class_qr_codes_class_id_fkey"
            columns: ["class_id"]
            isOneToOne: false
            referencedRelation: "student_class_eligibility"
            referencedColumns: ["class_id"]
          },
          {
            foreignKeyName: "class_qr_codes_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "class_qr_codes_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      class_status_logs: {
        Row: {
          changed_at: string
          class_id: string
          created_at: string
          id: string
          new_status: string
          old_status: string
          tenant_id: string
        }
        Insert: {
          changed_at?: string
          class_id: string
          created_at?: string
          id?: string
          new_status: string
          old_status: string
          tenant_id: string
        }
        Update: {
          changed_at?: string
          class_id?: string
          created_at?: string
          id?: string
          new_status?: string
          old_status?: string
          tenant_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "class_status_logs_class_id_fkey"
            columns: ["class_id"]
            isOneToOne: false
            referencedRelation: "classes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "class_status_logs_class_id_fkey"
            columns: ["class_id"]
            isOneToOne: false
            referencedRelation: "student_class_eligibility"
            referencedColumns: ["class_id"]
          },
          {
            foreignKeyName: "class_status_logs_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      class_waitlist: {
        Row: {
          added_date: string
          class_group_id: string
          created_at: string
          id: string
          metadata: Json | null
          notes: string | null
          notified_date: string | null
          position: number
          status: string
          student_id: string
          tenant_id: string
          updated_at: string
        }
        Insert: {
          added_date?: string
          class_group_id: string
          created_at?: string
          id?: string
          metadata?: Json | null
          notes?: string | null
          notified_date?: string | null
          position: number
          status?: string
          student_id: string
          tenant_id: string
          updated_at?: string
        }
        Update: {
          added_date?: string
          class_group_id?: string
          created_at?: string
          id?: string
          metadata?: Json | null
          notes?: string | null
          notified_date?: string | null
          position?: number
          status?: string
          student_id?: string
          tenant_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "class_waitlist_class_group_id_fkey"
            columns: ["class_group_id"]
            isOneToOne: false
            referencedRelation: "class_groups"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "class_waitlist_student_id_fkey"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "student_current_belt_details"
            referencedColumns: ["student_id"]
          },
          {
            foreignKeyName: "class_waitlist_student_id_fkey"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "students"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "class_waitlist_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      classes: {
        Row: {
          attendance_recorded: boolean
          branch_id: string
          class_group_id: string | null
          created_at: string
          deleted_at: string | null
          description: string | null
          end_time: string
          id: string
          instructor_id: string
          max_capacity: number | null
          metadata: Json | null
          name: string
          notes: string | null
          recurrence_pattern: string | null
          recurring: boolean
          start_time: string
          status: string
          tenant_id: string
          updated_at: string | null
        }
        Insert: {
          attendance_recorded?: boolean
          branch_id: string
          class_group_id?: string | null
          created_at?: string
          deleted_at?: string | null
          description?: string | null
          end_time: string
          id?: string
          instructor_id: string
          max_capacity?: number | null
          metadata?: Json | null
          name: string
          notes?: string | null
          recurrence_pattern?: string | null
          recurring?: boolean
          start_time: string
          status?: string
          tenant_id: string
          updated_at?: string | null
        }
        Update: {
          attendance_recorded?: boolean
          branch_id?: string
          class_group_id?: string | null
          created_at?: string
          deleted_at?: string | null
          description?: string | null
          end_time?: string
          id?: string
          instructor_id?: string
          max_capacity?: number | null
          metadata?: Json | null
          name?: string
          notes?: string | null
          recurrence_pattern?: string | null
          recurring?: boolean
          start_time?: string
          status?: string
          tenant_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "classes_branch_id_fkey"
            columns: ["branch_id"]
            isOneToOne: false
            referencedRelation: "branches"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "classes_class_group_id_fkey"
            columns: ["class_group_id"]
            isOneToOne: false
            referencedRelation: "class_groups"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "classes_instructor_id_fkey"
            columns: ["instructor_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "classes_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      cron_job_locks: {
        Row: {
          created_at: string
          execution_id: string | null
          expires_at: string
          id: string
          job_name: string
          locked_at: string
          locked_by: string
          metadata: Json | null
          updated_at: string
        }
        Insert: {
          created_at?: string
          execution_id?: string | null
          expires_at: string
          id?: string
          job_name: string
          locked_at?: string
          locked_by: string
          metadata?: Json | null
          updated_at?: string
        }
        Update: {
          created_at?: string
          execution_id?: string | null
          expires_at?: string
          id?: string
          job_name?: string
          locked_at?: string
          locked_by?: string
          metadata?: Json | null
          updated_at?: string
        }
        Relationships: []
      }
      enrollment_pauses: {
        Row: {
          created_at: string
          created_by: string
          enrollment_id: string
          id: string
          membership_id: string | null
          notes: string | null
          paused_at: string
          reason: string | null
          resumed_at: string | null
          tenant_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          created_by: string
          enrollment_id: string
          id?: string
          membership_id?: string | null
          notes?: string | null
          paused_at?: string
          reason?: string | null
          resumed_at?: string | null
          tenant_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          created_by?: string
          enrollment_id?: string
          id?: string
          membership_id?: string | null
          notes?: string | null
          paused_at?: string
          reason?: string | null
          resumed_at?: string | null
          tenant_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "enrollment_pauses_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "enrollment_pauses_enrollment_id_fkey"
            columns: ["enrollment_id"]
            isOneToOne: false
            referencedRelation: "class_group_enrollments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "enrollment_pauses_enrollment_id_fkey"
            columns: ["enrollment_id"]
            isOneToOne: false
            referencedRelation: "student_class_eligibility"
            referencedColumns: ["enrollment_id"]
          },
          {
            foreignKeyName: "enrollment_pauses_membership_id_fkey"
            columns: ["membership_id"]
            isOneToOne: false
            referencedRelation: "memberships"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "enrollment_pauses_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      expense_categories: {
        Row: {
          color: string | null
          created_at: string | null
          description: string | null
          id: string
          name: string
          tenant_id: string
          updated_at: string | null
        }
        Insert: {
          color?: string | null
          created_at?: string | null
          description?: string | null
          id?: string
          name: string
          tenant_id: string
          updated_at?: string | null
        }
        Update: {
          color?: string | null
          created_at?: string | null
          description?: string | null
          id?: string
          name?: string
          tenant_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "expense_categories_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      expenses: {
        Row: {
          amount: number
          category_id: string | null
          created_at: string | null
          currency: string
          description: string | null
          due_date: string | null
          id: string
          is_recurring: boolean | null
          metadata: Json | null
          paid_at: string | null
          payment_method_id: string | null
          recurrence_config: Json | null
          status: string
          supplier_document: string | null
          supplier_name: string
          tenant_id: string
          updated_at: string | null
        }
        Insert: {
          amount: number
          category_id?: string | null
          created_at?: string | null
          currency?: string
          description?: string | null
          due_date?: string | null
          id?: string
          is_recurring?: boolean | null
          metadata?: Json | null
          paid_at?: string | null
          payment_method_id?: string | null
          recurrence_config?: Json | null
          status: string
          supplier_document?: string | null
          supplier_name: string
          tenant_id: string
          updated_at?: string | null
        }
        Update: {
          amount?: number
          category_id?: string | null
          created_at?: string | null
          currency?: string
          description?: string | null
          due_date?: string | null
          id?: string
          is_recurring?: boolean | null
          metadata?: Json | null
          paid_at?: string | null
          payment_method_id?: string | null
          recurrence_config?: Json | null
          status?: string
          supplier_document?: string | null
          supplier_name?: string
          tenant_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "expenses_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "expense_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "expenses_payment_method_id_fkey"
            columns: ["payment_method_id"]
            isOneToOne: false
            referencedRelation: "payment_methods"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "expenses_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      instructor_belts: {
        Row: {
          awarded_at: string
          awarded_by: string | null
          belt_level_id: string
          created_at: string | null
          id: string
          instructor_id: string
          notes: string | null
          tenant_id: string
        }
        Insert: {
          awarded_at?: string
          awarded_by?: string | null
          belt_level_id: string
          created_at?: string | null
          id?: string
          instructor_id: string
          notes?: string | null
          tenant_id: string
        }
        Update: {
          awarded_at?: string
          awarded_by?: string | null
          belt_level_id?: string
          created_at?: string | null
          id?: string
          instructor_id?: string
          notes?: string | null
          tenant_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "instructor_belts_awarded_by_fkey"
            columns: ["awarded_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "instructor_belts_belt_level_id_fkey"
            columns: ["belt_level_id"]
            isOneToOne: false
            referencedRelation: "belt_levels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "instructor_belts_belt_level_id_fkey"
            columns: ["belt_level_id"]
            isOneToOne: false
            referencedRelation: "student_current_belt_details"
            referencedColumns: ["belt_level_id"]
          },
          {
            foreignKeyName: "instructor_belts_instructor_id_fkey"
            columns: ["instructor_id"]
            isOneToOne: false
            referencedRelation: "instructors"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "instructor_belts_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      instructors: {
        Row: {
          bio: string | null
          birth_date: string | null
          branch_id: string
          certification_level: string | null
          city: string | null
          complement: string | null
          contract_type: string
          created_at: string | null
          current_belt_id: string | null
          deleted_at: string | null
          emergency_contact_name: string | null
          emergency_contact_phone: string | null
          emergency_contact_relationship: string | null
          experience_years: number | null
          federation_registration: string | null
          gender: string | null
          has_cpr_certification: boolean | null
          has_first_aid_certification: boolean | null
          has_ibjjf_certification: boolean | null
          has_rules_course: boolean | null
          hire_date: string | null
          id: string
          is_active: boolean | null
          neighborhood: string | null
          payment_model: string | null
          payment_percentage: number | null
          payment_value: number | null
          postal_code: string | null
          search_vector: unknown | null
          specialties: string[] | null
          state: string | null
          street: string | null
          street_number: string | null
          teaching_notes: string | null
          tenant_id: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          bio?: string | null
          birth_date?: string | null
          branch_id: string
          certification_level?: string | null
          city?: string | null
          complement?: string | null
          contract_type: string
          created_at?: string | null
          current_belt_id?: string | null
          deleted_at?: string | null
          emergency_contact_name?: string | null
          emergency_contact_phone?: string | null
          emergency_contact_relationship?: string | null
          experience_years?: number | null
          federation_registration?: string | null
          gender?: string | null
          has_cpr_certification?: boolean | null
          has_first_aid_certification?: boolean | null
          has_ibjjf_certification?: boolean | null
          has_rules_course?: boolean | null
          hire_date?: string | null
          id?: string
          is_active?: boolean | null
          neighborhood?: string | null
          payment_model?: string | null
          payment_percentage?: number | null
          payment_value?: number | null
          postal_code?: string | null
          search_vector?: unknown | null
          specialties?: string[] | null
          state?: string | null
          street?: string | null
          street_number?: string | null
          teaching_notes?: string | null
          tenant_id: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          bio?: string | null
          birth_date?: string | null
          branch_id?: string
          certification_level?: string | null
          city?: string | null
          complement?: string | null
          contract_type?: string
          created_at?: string | null
          current_belt_id?: string | null
          deleted_at?: string | null
          emergency_contact_name?: string | null
          emergency_contact_phone?: string | null
          emergency_contact_relationship?: string | null
          experience_years?: number | null
          federation_registration?: string | null
          gender?: string | null
          has_cpr_certification?: boolean | null
          has_first_aid_certification?: boolean | null
          has_ibjjf_certification?: boolean | null
          has_rules_course?: boolean | null
          hire_date?: string | null
          id?: string
          is_active?: boolean | null
          neighborhood?: string | null
          payment_model?: string | null
          payment_percentage?: number | null
          payment_value?: number | null
          postal_code?: string | null
          search_vector?: unknown | null
          specialties?: string[] | null
          state?: string | null
          street?: string | null
          street_number?: string | null
          teaching_notes?: string | null
          tenant_id?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "instructors_branch_id_fkey"
            columns: ["branch_id"]
            isOneToOne: false
            referencedRelation: "branches"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "instructors_current_belt_id_fkey"
            columns: ["current_belt_id"]
            isOneToOne: false
            referencedRelation: "instructor_belts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "instructors_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      membership_status_logs: {
        Row: {
          change_reason: string | null
          changed_at: string
          changed_by: string | null
          id: string
          membership_id: string
          metadata: Json
          new_status: Database["public"]["Enums"]["membership_status"]
          old_status: Database["public"]["Enums"]["membership_status"] | null
          tenant_id: string
        }
        Insert: {
          change_reason?: string | null
          changed_at?: string
          changed_by?: string | null
          id?: string
          membership_id: string
          metadata?: Json
          new_status: Database["public"]["Enums"]["membership_status"]
          old_status?: Database["public"]["Enums"]["membership_status"] | null
          tenant_id: string
        }
        Update: {
          change_reason?: string | null
          changed_at?: string
          changed_by?: string | null
          id?: string
          membership_id?: string
          metadata?: Json
          new_status?: Database["public"]["Enums"]["membership_status"]
          old_status?: Database["public"]["Enums"]["membership_status"] | null
          tenant_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "membership_status_logs_membership_id_fkey"
            columns: ["membership_id"]
            isOneToOne: false
            referencedRelation: "memberships"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "membership_status_logs_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      memberships: {
        Row: {
          cancel_at: string | null
          canceled_at: string | null
          created_at: string
          end_date: string | null
          id: string
          metadata: Json
          next_billing_date: string | null
          plan_id: string
          reason: string | null
          start_date: string
          status: Database["public"]["Enums"]["membership_status"]
          student_id: string
          tenant_id: string
          updated_at: string | null
        }
        Insert: {
          cancel_at?: string | null
          canceled_at?: string | null
          created_at?: string
          end_date?: string | null
          id?: string
          metadata?: Json
          next_billing_date?: string | null
          plan_id: string
          reason?: string | null
          start_date?: string
          status?: Database["public"]["Enums"]["membership_status"]
          student_id: string
          tenant_id: string
          updated_at?: string | null
        }
        Update: {
          cancel_at?: string | null
          canceled_at?: string | null
          created_at?: string
          end_date?: string | null
          id?: string
          metadata?: Json
          next_billing_date?: string | null
          plan_id?: string
          reason?: string | null
          start_date?: string
          status?: Database["public"]["Enums"]["membership_status"]
          student_id?: string
          tenant_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "memberships_plan_id_fkey"
            columns: ["plan_id"]
            isOneToOne: false
            referencedRelation: "plans"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "memberships_student_id_fkey"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "student_current_belt_details"
            referencedColumns: ["student_id"]
          },
          {
            foreignKeyName: "memberships_student_id_fkey"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "students"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "memberships_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      modalities: {
        Row: {
          created_at: string
          id: string
          is_active: boolean
          name: string
          slug: string
          tenant_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          is_active?: boolean
          name: string
          slug: string
          tenant_id: string
        }
        Update: {
          created_at?: string
          id?: string
          is_active?: boolean
          name?: string
          slug?: string
          tenant_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "modalities_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      notification_preferences: {
        Row: {
          created_at: string | null
          email_enabled: boolean | null
          id: string
          in_app_enabled: boolean | null
          notification_type: string
          quiet_hours_end: string | null
          quiet_hours_start: string | null
          tenant_id: string
          timezone: string | null
          updated_at: string | null
          user_id: string
          whatsapp_enabled: boolean | null
        }
        Insert: {
          created_at?: string | null
          email_enabled?: boolean | null
          id?: string
          in_app_enabled?: boolean | null
          notification_type: string
          quiet_hours_end?: string | null
          quiet_hours_start?: string | null
          tenant_id: string
          timezone?: string | null
          updated_at?: string | null
          user_id: string
          whatsapp_enabled?: boolean | null
        }
        Update: {
          created_at?: string | null
          email_enabled?: boolean | null
          id?: string
          in_app_enabled?: boolean | null
          notification_type?: string
          quiet_hours_end?: string | null
          quiet_hours_start?: string | null
          tenant_id?: string
          timezone?: string | null
          updated_at?: string | null
          user_id?: string
          whatsapp_enabled?: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: "notification_preferences_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      notifications: {
        Row: {
          category: string
          channels: string[] | null
          created_at: string | null
          data: Json | null
          expires_at: string | null
          id: string
          message: string
          priority: string
          read_at: string | null
          scheduled_for: string | null
          status: string
          tenant_id: string
          title: string
          type: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          category: string
          channels?: string[] | null
          created_at?: string | null
          data?: Json | null
          expires_at?: string | null
          id?: string
          message: string
          priority?: string
          read_at?: string | null
          scheduled_for?: string | null
          status?: string
          tenant_id: string
          title: string
          type: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          category?: string
          channels?: string[] | null
          created_at?: string | null
          data?: Json | null
          expires_at?: string | null
          id?: string
          message?: string
          priority?: string
          read_at?: string | null
          scheduled_for?: string | null
          status?: string
          tenant_id?: string
          title?: string
          type?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "notifications_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      payment_methods: {
        Row: {
          created_at: string
          icon_name: string | null
          id: string
          name: string
          slug: string
        }
        Insert: {
          created_at?: string
          icon_name?: string | null
          id?: string
          name: string
          slug: string
        }
        Update: {
          created_at?: string
          icon_name?: string | null
          id?: string
          name?: string
          slug?: string
        }
        Relationships: []
      }
      payments: {
        Row: {
          amount: number
          attempt_count: number | null
          billing_cycle: string | null
          created_at: string
          currency: string
          description: string | null
          due_date: string | null
          id: string
          last_attempt_at: string | null
          membership_id: string | null
          metadata: Json | null
          overdue_date: string | null
          paid_at: string | null
          payment_method: string | null
          payment_type: Database["public"]["Enums"]["payment_type_enum"]
          reference_id: string | null
          search_vector: unknown | null
          status: string
          student_id: string
          tenant_id: string
          updated_at: string | null
        }
        Insert: {
          amount: number
          attempt_count?: number | null
          billing_cycle?: string | null
          created_at?: string
          currency?: string
          description?: string | null
          due_date?: string | null
          id: string
          last_attempt_at?: string | null
          membership_id?: string | null
          metadata?: Json | null
          overdue_date?: string | null
          paid_at?: string | null
          payment_method?: string | null
          payment_type?: Database["public"]["Enums"]["payment_type_enum"]
          reference_id?: string | null
          search_vector?: unknown | null
          status: string
          student_id: string
          tenant_id: string
          updated_at?: string | null
        }
        Update: {
          amount?: number
          attempt_count?: number | null
          billing_cycle?: string | null
          created_at?: string
          currency?: string
          description?: string | null
          due_date?: string | null
          id?: string
          last_attempt_at?: string | null
          membership_id?: string | null
          metadata?: Json | null
          overdue_date?: string | null
          paid_at?: string | null
          payment_method?: string | null
          payment_type?: Database["public"]["Enums"]["payment_type_enum"]
          reference_id?: string | null
          search_vector?: unknown | null
          status?: string
          student_id?: string
          tenant_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "payments_membership_id_fkey"
            columns: ["membership_id"]
            isOneToOne: false
            referencedRelation: "memberships"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payments_student_id_fkey"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "student_current_belt_details"
            referencedColumns: ["student_id"]
          },
          {
            foreignKeyName: "payments_student_id_fkey"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "students"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payments_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      plans: {
        Row: {
          access_config: Json
          base_plan_id: string | null
          benefits: string[]
          created_at: string
          created_by: string | null
          duration_config: Json
          id: string
          metadata: Json
          plan_type: Database["public"]["Enums"]["plan_type"]
          pricing_config: Json
          reason: string | null
          status: Database["public"]["Enums"]["plan_status"]
          tenant_id: string
          title: string
          updated_at: string | null
          version: number
        }
        Insert: {
          access_config?: Json
          base_plan_id?: string | null
          benefits?: string[]
          created_at?: string
          created_by?: string | null
          duration_config?: Json
          id?: string
          metadata?: Json
          plan_type?: Database["public"]["Enums"]["plan_type"]
          pricing_config?: Json
          reason?: string | null
          status?: Database["public"]["Enums"]["plan_status"]
          tenant_id: string
          title: string
          updated_at?: string | null
          version?: number
        }
        Update: {
          access_config?: Json
          base_plan_id?: string | null
          benefits?: string[]
          created_at?: string
          created_by?: string | null
          duration_config?: Json
          id?: string
          metadata?: Json
          plan_type?: Database["public"]["Enums"]["plan_type"]
          pricing_config?: Json
          reason?: string | null
          status?: Database["public"]["Enums"]["plan_status"]
          tenant_id?: string
          title?: string
          updated_at?: string | null
          version?: number
        }
        Relationships: [
          {
            foreignKeyName: "plans_base_plan_id_fkey"
            columns: ["base_plan_id"]
            isOneToOne: false
            referencedRelation: "plans"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "plans_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      stripe_webhooks: {
        Row: {
          created_at: string
          data: Json
          id: string
          processed: boolean
          stripe_event_id: string
          stripe_event_type: string
          tenant_id: string
        }
        Insert: {
          created_at?: string
          data: Json
          id: string
          processed?: boolean
          stripe_event_id: string
          stripe_event_type: string
          tenant_id: string
        }
        Update: {
          created_at?: string
          data?: Json
          id?: string
          processed?: boolean
          stripe_event_id?: string
          stripe_event_type?: string
          tenant_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "stripe_webhooks_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      student_belts: {
        Row: {
          awarded_at: string
          awarded_by: string
          belt_level_id: string
          created_at: string
          id: string
          notes: string | null
          student_id: string
          tenant_id: string
          updated_at: string | null
        }
        Insert: {
          awarded_at: string
          awarded_by: string
          belt_level_id: string
          created_at?: string
          id: string
          notes?: string | null
          student_id: string
          tenant_id: string
          updated_at?: string | null
        }
        Update: {
          awarded_at?: string
          awarded_by?: string
          belt_level_id?: string
          created_at?: string
          id?: string
          notes?: string | null
          student_id?: string
          tenant_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "student_belts_awarded_by_fkey"
            columns: ["awarded_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "student_belts_belt_level_id_fkey"
            columns: ["belt_level_id"]
            isOneToOne: false
            referencedRelation: "belt_levels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "student_belts_belt_level_id_fkey"
            columns: ["belt_level_id"]
            isOneToOne: false
            referencedRelation: "student_current_belt_details"
            referencedColumns: ["belt_level_id"]
          },
          {
            foreignKeyName: "student_belts_student_id_fkey"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "student_current_belt_details"
            referencedColumns: ["student_id"]
          },
          {
            foreignKeyName: "student_belts_student_id_fkey"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "students"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "student_belts_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      students: {
        Row: {
          allergies: string | null
          attendance_rate: number | null
          birth_date: string | null
          branch_id: string
          check_in_code: string | null
          city: string | null
          complement: string | null
          created_at: string
          current_belt_id: string | null
          deleted_at: string | null
          emergency_contact_name: string | null
          emergency_contact_phone: string | null
          emergency_contact_relationship: string | null
          financial_status: Database["public"]["Enums"]["FinancialStatus"]
          gender: string | null
          health_notes: string | null
          id: string
          is_minor: boolean | null
          last_attendance_date: string | null
          last_payment_date: string | null
          medical_conditions: string | null
          medications: string | null
          metadata: Json | null
          neighborhood: string | null
          next_payment_due: string | null
          payment_status: string | null
          postal_code: string | null
          registration_number: string | null
          requires_guardian_consent: boolean | null
          search_vector: unknown | null
          state: string | null
          street: string | null
          street_number: string | null
          tenant_id: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          allergies?: string | null
          attendance_rate?: number | null
          birth_date?: string | null
          branch_id: string
          check_in_code?: string | null
          city?: string | null
          complement?: string | null
          created_at?: string
          current_belt_id?: string | null
          deleted_at?: string | null
          emergency_contact_name?: string | null
          emergency_contact_phone?: string | null
          emergency_contact_relationship?: string | null
          financial_status?: Database["public"]["Enums"]["FinancialStatus"]
          gender?: string | null
          health_notes?: string | null
          id: string
          is_minor?: boolean | null
          last_attendance_date?: string | null
          last_payment_date?: string | null
          medical_conditions?: string | null
          medications?: string | null
          metadata?: Json | null
          neighborhood?: string | null
          next_payment_due?: string | null
          payment_status?: string | null
          postal_code?: string | null
          registration_number?: string | null
          requires_guardian_consent?: boolean | null
          search_vector?: unknown | null
          state?: string | null
          street?: string | null
          street_number?: string | null
          tenant_id: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          allergies?: string | null
          attendance_rate?: number | null
          birth_date?: string | null
          branch_id?: string
          check_in_code?: string | null
          city?: string | null
          complement?: string | null
          created_at?: string
          current_belt_id?: string | null
          deleted_at?: string | null
          emergency_contact_name?: string | null
          emergency_contact_phone?: string | null
          emergency_contact_relationship?: string | null
          financial_status?: Database["public"]["Enums"]["FinancialStatus"]
          gender?: string | null
          health_notes?: string | null
          id?: string
          is_minor?: boolean | null
          last_attendance_date?: string | null
          last_payment_date?: string | null
          medical_conditions?: string | null
          medications?: string | null
          metadata?: Json | null
          neighborhood?: string | null
          next_payment_due?: string | null
          payment_status?: string | null
          postal_code?: string | null
          registration_number?: string | null
          requires_guardian_consent?: boolean | null
          search_vector?: unknown | null
          state?: string | null
          street?: string | null
          street_number?: string | null
          tenant_id?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_student_current_belt"
            columns: ["current_belt_id"]
            isOneToOne: false
            referencedRelation: "student_belts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "students_branch_id_fkey"
            columns: ["branch_id"]
            isOneToOne: false
            referencedRelation: "branches"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "students_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "students_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      subscriptions: {
        Row: {
          cancel_at: string | null
          canceled_at: string | null
          created_at: string
          current_period_end: string | null
          current_period_start: string | null
          id: string
          status: Database["public"]["Enums"]["SubscriptionStatus"]
          student_id: string
          tenant_id: string
          updated_at: string | null
        }
        Insert: {
          cancel_at?: string | null
          canceled_at?: string | null
          created_at?: string
          current_period_end?: string | null
          current_period_start?: string | null
          id: string
          status: Database["public"]["Enums"]["SubscriptionStatus"]
          student_id: string
          tenant_id: string
          updated_at?: string | null
        }
        Update: {
          cancel_at?: string | null
          canceled_at?: string | null
          created_at?: string
          current_period_end?: string | null
          current_period_start?: string | null
          id?: string
          status?: Database["public"]["Enums"]["SubscriptionStatus"]
          student_id?: string
          tenant_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "subscriptions_student_id_fkey"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "student_current_belt_details"
            referencedColumns: ["student_id"]
          },
          {
            foreignKeyName: "subscriptions_student_id_fkey"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "students"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      template_variables: {
        Row: {
          category: string | null
          created_at: string | null
          data_type: string
          default_value: string | null
          description: string | null
          example_value: string | null
          id: string
          is_required: boolean | null
          template_type: string
          variable_key: string
          variable_name: string
        }
        Insert: {
          category?: string | null
          created_at?: string | null
          data_type: string
          default_value?: string | null
          description?: string | null
          example_value?: string | null
          id?: string
          is_required?: boolean | null
          template_type: string
          variable_key: string
          variable_name: string
        }
        Update: {
          category?: string | null
          created_at?: string | null
          data_type?: string
          default_value?: string | null
          description?: string | null
          example_value?: string | null
          id?: string
          is_required?: boolean | null
          template_type?: string
          variable_key?: string
          variable_name?: string
        }
        Relationships: []
      }
      tenant_belt_level_requirements: {
        Row: {
          belt_level_id: string
          created_at: string | null
          days_attended: number | null
          days_in_rank: number | null
          hours: number | null
          minimum_age: number | null
          promotion_fee: number | null
          sessions: number | null
          skill_requirements: boolean | null
          tenant_id: string
          updated_at: string | null
        }
        Insert: {
          belt_level_id: string
          created_at?: string | null
          days_attended?: number | null
          days_in_rank?: number | null
          hours?: number | null
          minimum_age?: number | null
          promotion_fee?: number | null
          sessions?: number | null
          skill_requirements?: boolean | null
          tenant_id: string
          updated_at?: string | null
        }
        Update: {
          belt_level_id?: string
          created_at?: string | null
          days_attended?: number | null
          days_in_rank?: number | null
          hours?: number | null
          minimum_age?: number | null
          promotion_fee?: number | null
          sessions?: number | null
          skill_requirements?: boolean | null
          tenant_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "tenant_belt_level_requirements_belt_level_id_fkey"
            columns: ["belt_level_id"]
            isOneToOne: false
            referencedRelation: "belt_levels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tenant_belt_level_requirements_belt_level_id_fkey"
            columns: ["belt_level_id"]
            isOneToOne: false
            referencedRelation: "student_current_belt_details"
            referencedColumns: ["belt_level_id"]
          },
          {
            foreignKeyName: "tenant_belt_level_requirements_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      tenant_general_payment_settings: {
        Row: {
          allow_installments: boolean
          convenience_fee: boolean
          created_at: string
          currency: string
          default_due_day: string
          pix_discount: boolean
          tenant_id: string
          updated_at: string | null
        }
        Insert: {
          allow_installments?: boolean
          convenience_fee?: boolean
          created_at?: string
          currency?: string
          default_due_day?: string
          pix_discount?: boolean
          tenant_id: string
          updated_at?: string | null
        }
        Update: {
          allow_installments?: boolean
          convenience_fee?: boolean
          created_at?: string
          currency?: string
          default_due_day?: string
          pix_discount?: boolean
          tenant_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "tenant_general_payment_settings_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: true
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      tenant_modalities: {
        Row: {
          created_at: string
          enabled: boolean
          modality_id: string
          tenant_id: string
        }
        Insert: {
          created_at?: string
          enabled?: boolean
          modality_id: string
          tenant_id: string
        }
        Update: {
          created_at?: string
          enabled?: boolean
          modality_id?: string
          tenant_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "tenant_modalities_modality_id_fkey"
            columns: ["modality_id"]
            isOneToOne: false
            referencedRelation: "modalities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tenant_modalities_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      tenant_modality_settings: {
        Row: {
          auto_assign_initial_rank: boolean | null
          created_at: string | null
          level_rank_style: string | null
          modality_id: string
          promotion_fee: number | null
          promotion_setting: string | null
          require_minimum_age: boolean | null
          require_sessions: boolean | null
          secondary_color: string | null
          tenant_id: string
          type: string | null
          updated_at: string | null
        }
        Insert: {
          auto_assign_initial_rank?: boolean | null
          created_at?: string | null
          level_rank_style?: string | null
          modality_id: string
          promotion_fee?: number | null
          promotion_setting?: string | null
          require_minimum_age?: boolean | null
          require_sessions?: boolean | null
          secondary_color?: string | null
          tenant_id: string
          type?: string | null
          updated_at?: string | null
        }
        Update: {
          auto_assign_initial_rank?: boolean | null
          created_at?: string | null
          level_rank_style?: string | null
          modality_id?: string
          promotion_fee?: number | null
          promotion_setting?: string | null
          require_minimum_age?: boolean | null
          require_sessions?: boolean | null
          secondary_color?: string | null
          tenant_id?: string
          type?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "tenant_modality_settings_modality_id_fkey"
            columns: ["modality_id"]
            isOneToOne: false
            referencedRelation: "modalities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tenant_modality_settings_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      tenant_notification_settings: {
        Row: {
          created_at: string | null
          email_enabled: boolean | null
          email_from_domain: string | null
          email_from_name: string | null
          notification_types: Json | null
          quiet_hours_end: string | null
          quiet_hours_start: string | null
          tenant_id: string
          timezone: string | null
          updated_at: string | null
          whatsapp_enabled: boolean | null
          whatsapp_opt_in_message: string | null
        }
        Insert: {
          created_at?: string | null
          email_enabled?: boolean | null
          email_from_domain?: string | null
          email_from_name?: string | null
          notification_types?: Json | null
          quiet_hours_end?: string | null
          quiet_hours_start?: string | null
          tenant_id: string
          timezone?: string | null
          updated_at?: string | null
          whatsapp_enabled?: boolean | null
          whatsapp_opt_in_message?: string | null
        }
        Update: {
          created_at?: string | null
          email_enabled?: boolean | null
          email_from_domain?: string | null
          email_from_name?: string | null
          notification_types?: Json | null
          quiet_hours_end?: string | null
          quiet_hours_start?: string | null
          tenant_id?: string
          timezone?: string | null
          updated_at?: string | null
          whatsapp_enabled?: boolean | null
          whatsapp_opt_in_message?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "tenant_notification_settings_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: true
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      tenant_payment_settings: {
        Row: {
          created_at: string
          enabled: boolean
          payment_method_id: string
          settings: Json | null
          tenant_id: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string
          enabled?: boolean
          payment_method_id: string
          settings?: Json | null
          tenant_id: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string
          enabled?: boolean
          payment_method_id?: string
          settings?: Json | null
          tenant_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "tenant_payment_settings_payment_method_id_fkey"
            columns: ["payment_method_id"]
            isOneToOne: false
            referencedRelation: "payment_methods"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tenant_payment_settings_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      tenants: {
        Row: {
          created_at: string
          deleted_at: string | null
          description: string | null
          favicon_storage_path: string | null
          favicon_url: string | null
          id: string
          logo_storage_path: string | null
          logo_url: string | null
          name: string
          owner_id: string | null
          primary_color: string | null
          secondary_color: string | null
          settings: Json | null
          slug: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string
          deleted_at?: string | null
          description?: string | null
          favicon_storage_path?: string | null
          favicon_url?: string | null
          id: string
          logo_storage_path?: string | null
          logo_url?: string | null
          name: string
          owner_id?: string | null
          primary_color?: string | null
          secondary_color?: string | null
          settings?: Json | null
          slug: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string
          deleted_at?: string | null
          description?: string | null
          favicon_storage_path?: string | null
          favicon_url?: string | null
          id?: string
          logo_storage_path?: string | null
          logo_url?: string | null
          name?: string
          owner_id?: string | null
          primary_color?: string | null
          secondary_color?: string | null
          settings?: Json | null
          slug?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      users: {
        Row: {
          avatar_storage_path: string | null
          avatar_url: string | null
          branch_id: string
          created_at: string
          deleted_at: string | null
          email: string
          first_name: string
          full_name: string | null
          guardian_document: string | null
          guardian_email: string | null
          guardian_name: string | null
          guardian_phone: string | null
          guardian_relationship: string | null
          id: string
          is_guardian_account: boolean | null
          is_minor: boolean | null
          last_name: string | null
          managed_student_id: string | null
          metadata: Json | null
          phone: string | null
          role: Database["public"]["Enums"]["UserRole"]
          search_vector: unknown | null
          status: string | null
          tenant_id: string
          updated_at: string | null
        }
        Insert: {
          avatar_storage_path?: string | null
          avatar_url?: string | null
          branch_id: string
          created_at?: string
          deleted_at?: string | null
          email: string
          first_name: string
          full_name?: string | null
          guardian_document?: string | null
          guardian_email?: string | null
          guardian_name?: string | null
          guardian_phone?: string | null
          guardian_relationship?: string | null
          id: string
          is_guardian_account?: boolean | null
          is_minor?: boolean | null
          last_name?: string | null
          managed_student_id?: string | null
          metadata?: Json | null
          phone?: string | null
          role: Database["public"]["Enums"]["UserRole"]
          search_vector?: unknown | null
          status?: string | null
          tenant_id: string
          updated_at?: string | null
        }
        Update: {
          avatar_storage_path?: string | null
          avatar_url?: string | null
          branch_id?: string
          created_at?: string
          deleted_at?: string | null
          email?: string
          first_name?: string
          full_name?: string | null
          guardian_document?: string | null
          guardian_email?: string | null
          guardian_name?: string | null
          guardian_phone?: string | null
          guardian_relationship?: string | null
          id?: string
          is_guardian_account?: boolean | null
          is_minor?: boolean | null
          last_name?: string | null
          managed_student_id?: string | null
          metadata?: Json | null
          phone?: string | null
          role?: Database["public"]["Enums"]["UserRole"]
          search_vector?: unknown | null
          status?: string | null
          tenant_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "users_branch_id_fkey"
            columns: ["branch_id"]
            isOneToOne: false
            referencedRelation: "branches"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "users_managed_student_id_fkey"
            columns: ["managed_student_id"]
            isOneToOne: false
            referencedRelation: "student_current_belt_details"
            referencedColumns: ["student_id"]
          },
          {
            foreignKeyName: "users_managed_student_id_fkey"
            columns: ["managed_student_id"]
            isOneToOne: false
            referencedRelation: "students"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "users_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      student_class_eligibility: {
        Row: {
          class_end_time: string | null
          class_group_id: string | null
          class_id: string | null
          class_name: string | null
          class_start_time: string | null
          class_status: string | null
          effective_from_date: string | null
          eligibility_reason: string | null
          enrollment_date: string | null
          enrollment_id: string | null
          enrollment_status: string | null
          is_eligible_for_class: boolean | null
          pause_periods: Json[] | null
          student_id: string | null
          tenant_id: string | null
        }
        Relationships: [
          {
            foreignKeyName: "class_group_enrollments_class_group_id_fkey"
            columns: ["class_group_id"]
            isOneToOne: false
            referencedRelation: "class_groups"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "class_group_enrollments_student_id_fkey"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "student_current_belt_details"
            referencedColumns: ["student_id"]
          },
          {
            foreignKeyName: "class_group_enrollments_student_id_fkey"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "students"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "class_group_enrollments_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      student_current_belt_details: {
        Row: {
          belt_color: string | null
          belt_level_id: string | null
          center_line_color: string | null
          degree: number | null
          label: string | null
          show_center_line: boolean | null
          sort_order: number | null
          stripe_color: string | null
          student_id: string | null
        }
        Relationships: []
      }
    }
    Functions: {
      acquire_cron_lock: {
        Args: {
          p_job_name: string
          p_locked_by: string
          p_execution_id: string
          p_timeout_minutes?: number
        }
        Returns: {
          success: boolean
          lock_id: string
          message: string
        }[]
      }
      admin_create_instructor: {
        Args: {
          p_id: string
          p_user_id: string
          p_tenant_id: string
          p_branch_id: string
          p_status?: string
          p_is_active?: boolean
          p_contract_type?: string
        }
        Returns: string
      }
      admin_create_user: {
        Args: {
          p_user_id: string
          p_email: string
          p_tenant_id: string
          p_role?: string
          p_first_name?: string
          p_last_name?: string
        }
        Returns: Json
      }
      admin_create_user_record: {
        Args: {
          p_id: string
          p_tenant_id: string
          p_branch_id: string
          p_email: string
          p_role: string
          p_first_name: string
          p_last_name: string
          p_full_name: string
          p_phone?: string
          p_avatar_url?: string
        }
        Returns: string
      }
      admin_create_user_v2: {
        Args: {
          p_user_id: string
          p_email: string
          p_tenant_id: string
          p_role?: string
          p_first_name?: string
          p_branch_id?: string
        }
        Returns: Json
      }
      calculate_due_date_for_tenant: {
        Args: { p_tenant_id: string; p_base_date?: string }
        Returns: string
      }
      calculate_is_minor: {
        Args: { birth_date: string }
        Returns: boolean
      }
      calculate_price: {
        Args: { p_plan_id: string; p_calculation_date?: string }
        Returns: Json
      }
      cancel_membership_payments: {
        Args: { p_membership_id: string; p_reason?: string }
        Returns: Json
      }
      check_pending_recurring_payments: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      check_students_financial_status_with_recurrence: {
        Args: Record<PropertyKey, never>
        Returns: {
          student_id: string
          current_status: Database["public"]["Enums"]["FinancialStatus"]
          calculated_status: Database["public"]["Enums"]["FinancialStatus"]
          needs_update: boolean
          overdue_amount: number
          urgent_amount: number
          next_payment_due: string
        }[]
      }
      cleanup_expired_cron_locks: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      create_cancellation_fee_payment: {
        Args: { p_membership_id: string; p_reason?: string }
        Returns: Json
      }
      create_graduation_fee_payment: {
        Args: {
          p_tenant_id: string
          p_student_id: string
          p_belt_level_id: string
          p_graduation_id: string
        }
        Returns: Json
      }
      create_initial_payment: {
        Args: { p_membership_id: string }
        Returns: Json
      }
      create_instructor_bypass_rls: {
        Args: {
          p_id: string
          p_user_id: string
          p_tenant_id: string
          p_branch_id: string
          p_status?: string
          p_is_active?: boolean
          p_contract_type?: string
        }
        Returns: string
      }
      create_manual_payment: {
        Args: {
          p_tenant_id: string
          p_student_id: string
          p_amount: number
          p_description: string
          p_due_date?: string
          p_metadata?: Json
        }
        Returns: Json
      }
      create_membership: {
        Args: {
          p_student_id: string
          p_plan_id: string
          p_start_date?: string
          p_metadata?: Json
        }
        Returns: Json
      }
      create_next_recurring_payment: {
        Args: { p_paid_payment_id: string }
        Returns: Json
      }
      create_plan: {
        Args:
          | {
              p_tenant_id: string
              p_title: string
              p_plan_type?: Database["public"]["Enums"]["plan_type"]
              p_pricing_config?: Json
              p_duration_config?: Json
              p_access_config?: Json
              p_benefits?: string[]
              p_metadata?: Json
            }
          | {
              p_title: string
              p_plan_type: Database["public"]["Enums"]["plan_type"]
              p_pricing_config: Json
              p_duration_config: Json
              p_access_config: Json
              p_benefits?: string[]
              p_metadata?: Json
            }
        Returns: Json
      }
      create_recurring_payments: {
        Args: { p_membership_id: string; p_payments_count?: number }
        Returns: Json
      }
      create_signup_fee_payment: {
        Args: { p_membership_id: string }
        Returns: Json
      }
      delete_student_graduation_admin: {
        Args: {
          p_tenant_id: string
          p_graduation_id: string
          p_deleted_by: string
        }
        Returns: Json
      }
      duplicate_plan: {
        Args: { p_plan_id: string; p_tenant_id: string }
        Returns: Json
      }
      end_session: {
        Args: { p_session_id: string }
        Returns: undefined
      }
      extract_date_immutable: {
        Args: { ts: string }
        Returns: string
      }
      generate_checkin_code: {
        Args: { p_tenant: string }
        Returns: string
      }
      get_active_plans: {
        Args: { p_tenant_id: string }
        Returns: Json
      }
      get_active_sessions: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          created_at: string
          ip: string
          user_agent: string
        }[]
      }
      get_all_instructor_specialties: {
        Args: Record<PropertyKey, never>
        Returns: string[]
      }
      get_brazil_current_month_bounds: {
        Args: Record<PropertyKey, never>
        Returns: {
          month_start: string
          month_end: string
        }[]
      }
      get_class_attendance_data: {
        Args: { p_class_id: string }
        Returns: {
          class_id: string
          class_name: string
          class_start_time: string
          class_end_time: string
          class_status: string
          class_group_id: string
          class_group_name: string
          instructor_name: string
          branch_name: string
          total_eligible_students: number
          total_present_students: number
          total_absent_students: number
          attendance_rate: number
          eligible_students: Json
          attendance_records: Json
        }[]
      }
      get_class_attendance_stats: {
        Args: { p_class_id: string }
        Returns: {
          total_eligible_students: number
          total_present_students: number
          total_absent_students: number
          attendance_rate: number
        }[]
      }
      get_current_date: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_daily_active_students_optimized: {
        Args: { p_tenant_id: string; p_days?: number }
        Returns: {
          date: string
          active_count: number
        }[]
      }
      get_daily_new_students_optimized: {
        Args: { p_tenant_id: string; p_days?: number }
        Returns: {
          date: string
          new_count: number
        }[]
      }
      get_daily_retention_rate_optimized: {
        Args: { p_tenant_id: string; p_days?: number }
        Returns: {
          date: string
          retention_rate: number
        }[]
      }
      get_eligible_students_for_class: {
        Args: { p_class_id: string; p_include_paused?: boolean }
        Returns: {
          student_id: string
          enrollment_id: string
          full_name: string
          first_name: string
          last_name: string
          email: string
          avatar_url: string
          check_in_code: string
          current_belt: Json
          enrollment_date: string
          effective_from_date: string
          is_currently_paused: boolean
          was_paused_during_class: boolean
          pause_reason: string
        }[]
      }
      get_expected_monthly_revenue: {
        Args: { tenant_uuid: string }
        Returns: number
      }
      get_expense_metrics: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      get_expense_metrics_with_amounts: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      get_instructor_current_belt_details: {
        Args: { instructor_id_param: string }
        Returns: {
          belt_level_id: string
          belt_color: string
          degree: number
          stripe_color: string
          show_center_line: boolean
          center_line_color: string
          label: string
          sort_order: number
          awarded_at: string
          awarded_by: string
          awarded_by_first_name: string
          awarded_by_last_name: string
          awarded_by_full_name: string
          modality_name: string
          notes: string
        }[]
      }
      get_last_paid_payment_for_membership: {
        Args: { p_membership_id: string }
        Returns: {
          payment_id: string
          due_date: string
          paid_at: string
          amount: number
        }[]
      }
      get_membership_statistics: {
        Args: { p_start_date?: string; p_end_date?: string }
        Returns: Json
      }
      get_modality_requirements: {
        Args: { p_tenant_id: string; p_modality_id: string }
        Returns: {
          belt_level_id: string
          sessions: number
          hours: number
          days_in_rank: number
          days_attended: number
          skill_requirements: boolean
          minimum_age: number
          promotion_fee: number
          created_at: string
          updated_at: string
          belt_label: string
          belt_color: string
          degree: number
          sort_order: number
        }[]
      }
      get_monthly_new_students_optimized: {
        Args: { p_tenant_id: string; p_months?: number }
        Returns: {
          month: number
          year: number
          new_count: number
        }[]
      }
      get_monthly_retention_rate_optimized: {
        Args: { p_tenant_id: string; p_months?: number }
        Returns: {
          month: number
          year: number
          retention_rate: number
        }[]
      }
      get_monthly_revenue_average: {
        Args: { tenant_uuid: string }
        Returns: number
      }
      get_monthly_revenue_optimized: {
        Args: { p_tenant_id: string; p_months?: number }
        Returns: {
          month: number
          year: number
          revenue_total: number
        }[]
      }
      get_next_charges: {
        Args: { p_tenant_id: string; p_limit?: number }
        Returns: {
          payment_id: string
          student_name: string
          plan_title: string
          amount: number
          due_date: string
          status: string
          payment_method: string
        }[]
      }
      get_next_payment_for_membership: {
        Args: { p_membership_id: string }
        Returns: {
          payment_id: string
          due_date: string
          amount: number
          status: string
          payment_type: Database["public"]["Enums"]["payment_type_enum"]
        }[]
      }
      get_overdue_expenses: {
        Args: { p_tenant_id?: string }
        Returns: {
          id: string
          tenant_id: string
          supplier_name: string
          amount: number
          due_date: string
          status: string
          overdue_days: number
          created_at: string
          updated_at: string
        }[]
      }
      get_overdue_expenses_count: {
        Args: { p_tenant_id?: string }
        Returns: number
      }
      get_overdue_expenses_value: {
        Args: { p_tenant_id?: string }
        Returns: number
      }
      get_overdue_payments: {
        Args: { p_tenant_id?: string }
        Returns: Json
      }
      get_overdue_payments_for_notifications: {
        Args: { p_tenant_id?: string }
        Returns: Json
      }
      get_overdue_tuitions_count: {
        Args: { tenant_uuid: string }
        Returns: number
      }
      get_overdue_tuitions_value: {
        Args: { tenant_uuid: string }
        Returns: number
      }
      get_paid_tuitions_count: {
        Args: { tenant_uuid: string }
        Returns: number
      }
      get_paid_tuitions_value: {
        Args: { tenant_uuid: string }
        Returns: number
      }
      get_payment_by_id: {
        Args: { p_payment_id: string }
        Returns: Json
      }
      get_payment_metrics: {
        Args: {
          p_tenant_id: string
          p_start_date?: string
          p_end_date?: string
        }
        Returns: Json
      }
      get_payments_by_membership: {
        Args: { p_membership_id: string; p_limit?: number; p_offset?: number }
        Returns: Json
      }
      get_payments_by_student: {
        Args: { p_student_id: string; p_limit?: number; p_offset?: number }
        Returns: Json
      }
      get_payments_for_reminders: {
        Args: { p_tenant_id?: string }
        Returns: Json
      }
      get_plan_latest_version: {
        Args: { p_base_plan_id: string }
        Returns: Json
      }
      get_plan_usage_analytics: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      get_recurrent_metrics: {
        Args: { p_tenant_id: string; p_start_date: string; p_end_date: string }
        Returns: {
          alunos_em_dia: number
          receita_mensal: number
          alunos_atrasados: number
          taxa_sucesso: number
        }[]
      }
      get_scheduled_tuitions_count: {
        Args: { tenant_uuid: string }
        Returns: number
      }
      get_scheduled_tuitions_value: {
        Args: { tenant_uuid: string }
        Returns: number
      }
      get_student_active_memberships: {
        Args: { p_student_id: string }
        Returns: Json
      }
      get_student_belt_requirements: {
        Args: { student_id_param: string; tenant_id_param: string }
        Returns: {
          requirement_id: string
          requirement_name: string
          requirement_description: string
          requirement_category: string
          is_completed: boolean
          completion_date: string
          progress_percentage: number
        }[]
      }
      get_student_current_belt_details: {
        Args: { student_id_param: string }
        Returns: {
          belt_level_id: string
          belt_color: string
          degree: number
          stripe_color: string
          show_center_line: boolean
          center_line_color: string
          label: string
          sort_order: number
          modality_id: string
          awarded_at: string
        }[]
      }
      get_student_streak: {
        Args: { student_id_param: string }
        Returns: {
          current_streak: number
          longest_streak: number
        }[]
      }
      get_students_chart_data_optimized: {
        Args: { p_tenant_id: string; p_days?: number }
        Returns: {
          day_date: string
          active_count: number
          inactive_count: number
        }[]
      }
      get_students_metrics_optimized: {
        Args: { p_tenant_id: string }
        Returns: {
          active_students_now: number
          active_students_last: number
          new_students_now: number
          new_students_last: number
        }[]
      }
      get_students_with_users: {
        Args: { student_ids: string[] }
        Returns: {
          student_id: string
          user_id: string
          first_name: string
          last_name: string
          full_name: string
        }[]
      }
      get_tenant_modality_settings: {
        Args: { p_tenant_id: string; p_modality_id: string }
        Returns: Json
      }
      get_tenant_settings: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      get_tuition_metrics: {
        Args: { tenant_uuid: string }
        Returns: {
          paid_tuitions: number
          scheduled_tuitions: number
          overdue_tuitions: number
          monthly_revenue_average: number
        }[]
      }
      get_tuition_value_metrics: {
        Args: { tenant_uuid: string }
        Returns: {
          paid_tuitions_value: number
          scheduled_tuitions_value: number
          overdue_tuitions_value: number
          monthly_revenue_average: number
          expected_monthly_revenue: number
        }[]
      }
      get_user_by_id_or_email: {
        Args: { p_id: string; p_email: string }
        Returns: {
          avatar_storage_path: string | null
          avatar_url: string | null
          branch_id: string
          created_at: string
          deleted_at: string | null
          email: string
          first_name: string
          full_name: string | null
          guardian_document: string | null
          guardian_email: string | null
          guardian_name: string | null
          guardian_phone: string | null
          guardian_relationship: string | null
          id: string
          is_guardian_account: boolean | null
          is_minor: boolean | null
          last_name: string | null
          managed_student_id: string | null
          metadata: Json | null
          phone: string | null
          role: Database["public"]["Enums"]["UserRole"]
          search_vector: unknown | null
          status: string | null
          tenant_id: string
          updated_at: string | null
        }[]
      }
      get_users_with_inconsistent_names: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          users_full_name: string
          auth_full_name: string
          is_inconsistent: boolean
        }[]
      }
      graduate_student_admin: {
        Args: {
          p_tenant_id: string
          p_student_id: string
          p_belt_level_id: string
          p_awarded_by: string
          p_awarded_at?: string
        }
        Returns: Json
      }
      gtrgm_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gtrgm_decompress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gtrgm_in: {
        Args: { "": unknown }
        Returns: unknown
      }
      gtrgm_options: {
        Args: { "": unknown }
        Returns: undefined
      }
      gtrgm_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      increment_ai_message_count: {
        Args: { p_user_id: string; p_tenant_id: string; p_usage_date: string }
        Returns: undefined
      }
      initialize_belt_levels_from_preset: {
        Args: {
          p_tenant_id: string
          p_modality_id: string
          p_preset_data: Json
        }
        Returns: {
          id: string
          belt_color: Database["public"]["Enums"]["BeltColor"]
          degree: number
          label: string
          sort_order: number
          stripe_color: string
          show_center_line: boolean
          center_line_color: string
        }[]
      }
      is_student_eligible_for_class: {
        Args: { p_student_id: string; p_class_id: string }
        Returns: {
          is_eligible: boolean
          reason: string
          enrollment_date: string
          effective_from_date: string
          was_paused: boolean
        }[]
      }
      is_student_paused_during_class: {
        Args: {
          p_enrollment_id: string
          p_class_start_time: string
          p_class_end_time: string
        }
        Returns: boolean
      }
      jwt_tenant_id: {
        Args: { claims: Json }
        Returns: Json
      }
      list_tenant_belt_level_requirements: {
        Args: { p_tenant_id: string; p_belt_level_ids?: string[] }
        Returns: {
          belt_level_id: string
          created_at: string | null
          days_attended: number | null
          days_in_rank: number | null
          hours: number | null
          minimum_age: number | null
          promotion_fee: number | null
          sessions: number | null
          skill_requirements: boolean | null
          tenant_id: string
          updated_at: string | null
        }[]
      }
      list_tenant_modality_settings: {
        Args: { p_tenant_id: string }
        Returns: {
          auto_assign_initial_rank: boolean | null
          created_at: string | null
          level_rank_style: string | null
          modality_id: string
          promotion_fee: number | null
          promotion_setting: string | null
          require_minimum_age: boolean | null
          require_sessions: boolean | null
          secondary_color: string | null
          tenant_id: string
          type: string | null
          updated_at: string | null
        }[]
      }
      mark_overdue_payments: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      membership_overview: {
        Args: { p_start_date?: string; p_end_date?: string }
        Returns: Json
      }
      pause_membership: {
        Args: { p_membership_id: string; p_reason?: string; p_metadata?: Json }
        Returns: Json
      }
      process_all_recurring_billing: {
        Args: { p_tenant_id?: string }
        Returns: Json
      }
      process_financial_status_updates_with_recurrence: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      process_membership_billing: {
        Args: { p_membership_id: string }
        Returns: Json
      }
      process_overdue_expenses_v2: {
        Args: { p_tenant_id?: string }
        Returns: Json
      }
      process_overdue_payments: {
        Args: { p_tenant_id?: string }
        Returns: Json
      }
      process_overdue_payments_v2: {
        Args: { p_tenant_id?: string }
        Returns: Json
      }
      process_pending_recurring_payments: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      publish_plan: {
        Args: { p_plan_id: string; p_tenant_id: string }
        Returns: Json
      }
      release_cron_lock: {
        Args: { p_job_name: string; p_locked_by: string }
        Returns: {
          success: boolean
          message: string
        }[]
      }
      set_limit: {
        Args: { "": number }
        Returns: number
      }
      set_tenant_setting: {
        Args: { p_setting_key: string; p_setting_value: Json }
        Returns: Json
      }
      show_limit: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      show_trgm: {
        Args: { "": string }
        Returns: string[]
      }
      toggle_tenant_payment_method: {
        Args: {
          p_tenant_id: string
          p_payment_method_id: string
          p_enabled: boolean
        }
        Returns: undefined
      }
      update_all_students_financial_status: {
        Args: Record<PropertyKey, never>
        Returns: {
          student_id: string
          old_status: Database["public"]["Enums"]["FinancialStatus"]
          new_status: Database["public"]["Enums"]["FinancialStatus"]
        }[]
      }
      update_daily_overdue_payments: {
        Args: Record<PropertyKey, never>
        Returns: {
          tenant_uuid: string
          payments_marked_overdue: number
        }[]
      }
      update_membership_status: {
        Args: {
          p_membership_id: string
          p_new_status: Database["public"]["Enums"]["membership_status"]
          p_reason?: string
        }
        Returns: Json
      }
      update_payment_status: {
        Args: { p_payment_id: string; p_new_status: string; p_reason?: string }
        Returns: Json
      }
      update_student_financial_status: {
        Args: { student_id_param: string }
        Returns: Database["public"]["Enums"]["FinancialStatus"]
      }
      update_tenant_setting: {
        Args: { p_tenant_id: string; p_key: string; p_value: Json }
        Returns: undefined
      }
      upsert_belt_levels: {
        Args: { p_tenant_id: string; p_modality_id: string; p_levels: Json }
        Returns: Json
      }
      upsert_belt_levels_admin: {
        Args: { p_tenant_id: string; p_modality_id: string; p_levels: Json }
        Returns: Json
      }
      upsert_tenant_belt_level_requirement: {
        Args: {
          p_tenant_id: string
          p_belt_level_id: string
          p_sessions?: number
          p_hours?: number
          p_days_in_rank?: number
          p_days_attended?: number
          p_skill_requirements?: boolean
          p_minimum_age?: number
          p_promotion_fee?: number
        }
        Returns: undefined
      }
      upsert_tenant_modality_settings: {
        Args:
          | {
              p_tenant_id: string
              p_modality_id: string
              p_type?: string
              p_level_rank_style?: string
              p_secondary_color?: string
              p_auto_assign_initial_rank?: boolean
              p_promotion_setting?: string
              p_promotion_fee?: number
            }
          | {
              p_tenant_id: string
              p_modality_id: string
              p_type?: string
              p_level_rank_style?: string
              p_secondary_color?: string
              p_auto_assign_initial_rank?: boolean
              p_promotion_setting?: string
              p_promotion_fee?: number
              p_require_sessions?: boolean
              p_require_minimum_age?: boolean
            }
        Returns: undefined
      }
      validate_plan_config: {
        Args: {
          p_pricing_config: Json
          p_duration_config: Json
          p_access_config: Json
        }
        Returns: Json
      }
      verify_tenant_access: {
        Args: { p_email: string; p_tenant_id: string }
        Returns: Json
      }
    }
    Enums: {
      BeltColor: "white" | "blue" | "purple" | "brown" | "black"
      FinancialStatus: "up_to_date" | "pending" | "overdue" | "no_data"
      membership_status: "active" | "paused" | "canceled" | "expired"
      payment_type_enum:
        | "recurring"
        | "signup_fee"
        | "graduation_fee"
        | "late_fee"
        | "cancellation_fee"
        | "manual"
        | "product"
        | "initial_payment"
      plan_status: "draft" | "active" | "archived" | "paused"
      plan_type: "individual" | "family" | "corporate"
      StudentStatus: "active" | "inactive" | "blocked"
      SubscriptionStatus: "active" | "canceled" | "past_due" | "unpaid"
      tenant_error_code:
        | "tenant_access_denied"
        | "tenant_not_found"
        | "user_not_in_tenant"
      UserRole: "admin" | "instructor" | "student"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      BeltColor: ["white", "blue", "purple", "brown", "black"],
      FinancialStatus: ["up_to_date", "pending", "overdue", "no_data"],
      membership_status: ["active", "paused", "canceled", "expired"],
      payment_type_enum: [
        "recurring",
        "signup_fee",
        "graduation_fee",
        "late_fee",
        "cancellation_fee",
        "manual",
        "product",
        "initial_payment",
      ],
      plan_status: ["draft", "active", "archived", "paused"],
      plan_type: ["individual", "family", "corporate"],
      StudentStatus: ["active", "inactive", "blocked"],
      SubscriptionStatus: ["active", "canceled", "past_due", "unpaid"],
      tenant_error_code: [
        "tenant_access_denied",
        "tenant_not_found",
        "user_not_in_tenant",
      ],
      UserRole: ["admin", "instructor", "student"],
    },
  },
} as const
